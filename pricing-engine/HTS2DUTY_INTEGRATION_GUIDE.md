# HTS2Duty Integration Guide

## Overview

This document describes the integration of the HTS2Duty functionality into the pricing-engine application. The integration replaces external API calls for tariff data with a local HTS2Duty service, providing better performance, reliability, and cost efficiency.

## What Was Integrated

### 1. HTS2Duty Service Wrapper
- **File**: `app/services/hts2duty_service.py`
- **Purpose**: Provides a clean interface to the HTS2Duty functionality
- **Features**:
  - Singleton pattern for efficient resource usage
  - Error handling and fallback mechanisms
  - Compatible API interface with existing external services

### 2. Configuration Updates
- **File**: `app/config.py`
- **Added**:
  - `USE_LOCAL_HTS2DUTY`: Boolean flag to enable/disable local service
  - `HTS2DUTY_SERVICE_URL`: URL for the HTS2Duty service (for future use)

### 3. Environment Configuration
- **File**: `.env`
- **Added**:
  - `USE_LOCAL_HTS2DUTY=true`: Enables local HTS2Duty service
  - `HTS2DUTY_SERVICE_URL=http://localhost:8080`: Service URL configuration

## Services Updated

### 1. TradeRankerService
- **File**: `app/services/trade_ranker_service.py`
- **Changes**:
  - Added import for HTS2Duty configuration
  - Modified `fetch_tariff_data()` method to use local service when enabled
  - Added `_fetch_external_tariff_data()` method as fallback
  - Maintains backward compatibility with external APIs

### 2. TradeFinderService
- **File**: `app/services/trade_finder_service.py`
- **Changes**:
  - Added configuration import in constructor
  - Modified `_fetch_tariff_data()` method to use local service
  - Added external API fallback method
  - Preserves existing functionality while adding local capability

### 3. IntegratedCountryService (Controller)
- **File**: `app/controllers/trade_finder_controller.py`
- **Changes**:
  - Added HTS2Duty configuration to constructor
  - Updated `_fetch_tariff_data()` method to use local service
  - Added external API fallback functionality
  - Maintains API compatibility

## How It Works

### 1. Service Selection Logic
```python
if USE_LOCAL_HTS2DUTY:
    # Use local HTS2Duty service
    hts2duty_service = get_hts2duty_service()
    result = hts2duty_service.get_tariff_data(hs_code, country)
    
    if 'error' in result:
        # Fallback to external API if local service fails
        result = self._fetch_external_tariff_data(hs_code, country)
else:
    # Use external API directly
    result = self._fetch_external_tariff_data(hs_code, country)
```

### 2. HTS2Duty Service Flow
1. **Initialization**: Load HTS indices and tariff service components
2. **Code Standardization**: Convert HS codes to standard format (XXXX.XX.XX.XX)
3. **Lookup**: Search HTS data using semantic lookup
4. **Tariff Calculation**: Calculate country-specific tariffs
5. **Response Formatting**: Format response to match external API structure

### 3. Fallback Mechanism
- If local HTS2Duty service fails, automatically falls back to external API
- Logs warnings for debugging while maintaining service availability
- Ensures zero downtime during integration issues

## Benefits

### 1. Performance
- **Faster Response Times**: Local processing eliminates network latency
- **No Rate Limits**: No external API rate limiting constraints
- **Offline Capability**: Works without internet connectivity

### 2. Reliability
- **Reduced Dependencies**: Less reliance on external services
- **Better Uptime**: Local service availability under your control
- **Fallback Protection**: Automatic fallback to external APIs if needed

### 3. Cost Efficiency
- **No API Costs**: Eliminates external API usage fees
- **Reduced Bandwidth**: No external network calls for tariff data
- **Scalability**: Handle more requests without additional costs

### 4. Data Quality
- **Consistent Data**: Same data source for all tariff calculations
- **Up-to-date Information**: Local HTS data can be updated as needed
- **Enhanced Features**: Access to additional tariff calculation features

## Configuration Options

### Environment Variables
```bash
# Enable/disable local HTS2Duty service
USE_LOCAL_HTS2DUTY=true

# HTS2Duty service URL (for future distributed deployments)
HTS2DUTY_SERVICE_URL=http://localhost:8080
```

### Runtime Configuration
- **Dynamic Switching**: Can be toggled without code changes
- **Service-Level Control**: Each service respects the global configuration
- **Fallback Behavior**: Automatic fallback ensures service continuity

## Testing

### Test Script
Run the integration test script:
```bash
cd pricing-engine
python test_hts2duty_integration.py
```

### Test Coverage
- Configuration validation
- HTS2Duty service functionality
- Service integration points
- Fallback mechanisms

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure HTS2Duty dependencies are installed
   - Check Python path configuration
   - Verify file permissions

2. **Service Initialization Failures**
   - Check HTS data files exist
   - Verify indices directory permissions
   - Review log files for specific errors

3. **Fallback Activation**
   - Monitor logs for fallback usage
   - Check external API connectivity
   - Verify configuration settings

### Debugging
- Enable debug logging: `logging.getLogger().setLevel(logging.DEBUG)`
- Check service logs for detailed error information
- Use the test script to isolate issues

## Future Enhancements

### Planned Improvements
1. **Distributed Deployment**: Support for remote HTS2Duty services
2. **Caching Layer**: Add Redis caching for frequently requested data
3. **Monitoring**: Add metrics and health checks
4. **Data Updates**: Automated HTS data refresh mechanisms

### Extension Points
- Custom tariff calculation rules
- Additional country-specific logic
- Enhanced error handling and retry mechanisms
- Performance optimization and caching strategies

## Maintenance

### Regular Tasks
1. **Monitor Performance**: Check response times and error rates
2. **Update HTS Data**: Refresh tariff data as needed
3. **Review Logs**: Monitor for errors and optimization opportunities
4. **Test Fallbacks**: Periodically test external API fallback functionality

### Version Updates
- Keep HTS2Duty components updated
- Test integration after updates
- Maintain backward compatibility
- Document any breaking changes

## Support

For issues or questions regarding the HTS2Duty integration:
1. Check the troubleshooting section above
2. Review application logs for error details
3. Run the test script to identify specific issues
4. Consult the HTS2Duty documentation for service-specific problems
