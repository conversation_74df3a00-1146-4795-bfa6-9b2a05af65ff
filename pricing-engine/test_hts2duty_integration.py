#!/usr/bin/env python3
"""
Test script to verify HTS2Duty integration is working correctly.
This script tests the local HTS2Duty service functionality.
"""

import os
import sys
import logging

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_hts2duty_service():
    """Test the HTS2Duty service directly."""
    try:
        from app.services.hts2duty_service import get_hts2duty_service
        
        logger.info("Testing HTS2Duty service...")
        
        # Get the service instance
        hts2duty_service = get_hts2duty_service()
        
        # Test with a sample HS code and country
        test_hs_code = "2921.19.11.00"
        test_country = "China"
        
        logger.info(f"Testing with HS code: {test_hs_code}, Country: {test_country}")
        
        # Get tariff data
        result = hts2duty_service.get_tariff_data(test_hs_code, test_country)
        
        logger.info(f"Result: {result}")
        
        if 'error' in result:
            logger.error(f"Error in HTS2Duty service: {result['error']}")
            return False
        else:
            logger.info("HTS2Duty service test successful!")
            return True
            
    except Exception as e:
        logger.error(f"Error testing HTS2Duty service: {str(e)}")
        return False

def test_trade_ranker_integration():
    """Test the trade ranker service integration."""
    try:
        from app.services.trade_ranker_service import TradeRankerService
        
        logger.info("Testing TradeRankerService integration...")
        
        # Create service instance
        service = TradeRankerService()
        
        # Test with sample data
        test_hs_code = "2921.19.11.00"
        test_country = "China"
        
        logger.info(f"Testing fetch_tariff_data with HS code: {test_hs_code}, Country: {test_country}")
        
        # Test the fetch_tariff_data method
        result = service.fetch_tariff_data(test_country, test_hs_code)
        
        logger.info(f"TradeRankerService result: {result}")
        
        if 'error' in result:
            logger.warning(f"Warning in TradeRankerService: {result['error']}")
            return True  # This might be expected if external API is down
        else:
            logger.info("TradeRankerService integration test successful!")
            return True
            
    except Exception as e:
        logger.error(f"Error testing TradeRankerService integration: {str(e)}")
        return False

def test_trade_finder_integration():
    """Test the trade finder service integration."""
    try:
        from app.services.trade_finder_service import TradeFinderService
        
        logger.info("Testing TradeFinderService integration...")
        
        # Create service instance
        service = TradeFinderService()
        
        # Test with sample data
        test_hs_code = "2921.19.11.00"
        test_country = "China"
        
        logger.info(f"Testing _fetch_tariff_data with HS code: {test_hs_code}, Country: {test_country}")
        
        # Test the _fetch_tariff_data method
        result = service._fetch_tariff_data(test_hs_code, test_country)
        
        logger.info(f"TradeFinderService result: {result}")
        
        if 'error' in result:
            logger.warning(f"Warning in TradeFinderService: {result['error']}")
            return True  # This might be expected if external API is down
        else:
            logger.info("TradeFinderService integration test successful!")
            return True
            
    except Exception as e:
        logger.error(f"Error testing TradeFinderService integration: {str(e)}")
        return False

def test_config():
    """Test configuration settings."""
    try:
        from app.config import USE_LOCAL_HTS2DUTY, HTS2DUTY_SERVICE_URL
        
        logger.info("Testing configuration...")
        logger.info(f"USE_LOCAL_HTS2DUTY: {USE_LOCAL_HTS2DUTY}")
        logger.info(f"HTS2DUTY_SERVICE_URL: {HTS2DUTY_SERVICE_URL}")
        
        if USE_LOCAL_HTS2DUTY:
            logger.info("✓ Local HTS2Duty service is enabled")
        else:
            logger.warning("⚠ Local HTS2Duty service is disabled")
            
        return True
        
    except Exception as e:
        logger.error(f"Error testing configuration: {str(e)}")
        return False

def main():
    """Run all tests."""
    logger.info("=" * 60)
    logger.info("HTS2Duty Integration Test Suite")
    logger.info("=" * 60)
    
    tests = [
        ("Configuration", test_config),
        ("HTS2Duty Service", test_hts2duty_service),
        ("TradeRankerService Integration", test_trade_ranker_integration),
        ("TradeFinderService Integration", test_trade_finder_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✓ {test_name} test PASSED")
            else:
                logger.error(f"✗ {test_name} test FAILED")
        except Exception as e:
            logger.error(f"✗ {test_name} test FAILED with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("Test Summary")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! HTS2Duty integration is working correctly.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
