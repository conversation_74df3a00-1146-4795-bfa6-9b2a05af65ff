# import logging
# import psycopg2
# import time
# import json
# import os
# import re
# import requests
# from pathlib import Path

# from typing import Dict, Any, List, Optional

# from app import db
# from app.config import DB_CONFIG
# from app.services.perplexity_client import PerplexityClient

# logger = logging.getLogger(__name__)

# # ORIGINAL: Static dictionary of chemical name variations
# # MODIFIED: Enhanced with more variations from volza_query_engine and added file-based persistence
# chemical_name_variations = {
#     "Epoxy Resin" : ["Epoxide Resin","Epoxide Resins","Epoxy Resins","Epoxy"],
#     "MEA Triazine" : ['MEA Triazine', 'Triazine', 'MEATriazine', 'Monoethanolamine Triazine', 'Mono Ethanol Amine Triazine', 'MEA TRIAZINE 78%', 'MEA TRIAZINE 42%', 'MEA TRIAZINE 78% (TRIAZINE RING:OTHER NSPF)'],
#     "MEA (MONO ETHANOL AMINE)": ['MEA (<PERSON>ONO ETHANOL AMINE)', 'MEA', 'MON<PERSON>THANOLAMINE', 'ethanolamine', '2-aminoethanol', '2-hydroxyethylamine'],
#     "MORPHOLINE (HETEROCYCLIC COMPOUND:OTH NSPF)": ['MORPHOLINE (HETEROCYCLIC COMPOUND:OTH NSPF)', 'Morpholine'],
#     "Triazine Fungicide (DISINFECTANTS, AROMATIC OR MOD)": ['Triazine Fungicide (DISINFECTANTS, AROMATIC OR MOD)', 'Triazine Fungicide', 'MSTACK HS 7800 - Triazine Fungicide (DISINFECTANTS, AROMATIC OR MOD) - MEA TRIAZINE 78%'],
#     "Alkyl Pyridine Quat (APQ)" : ['Alkyl Pyridine Quat (APQ)', 'APQ', 'Alkyl Pyridine Quat', 'Alkyl Pyridine Quat (APQ) - (PYRIDINE AND ITS SALTS)'],
#     "Epoxide Resins" : ['Chain Stopped Short Oil Alkyd Resin', 'ALKYD RESINS IN PRIMARY', 'Chain Stopped Short Oil Alkyd Resin (ALKYD RESINS IN PRIMARY FORM)', 'EPOXIDE RESINS', 'ALKYD RESIN WB4868', 'Epoxy Resin','Epoxy Resins'],
#     "ACETONE, OTHR THAN FROM CU" : ['ACETONE', 'ACETONE, OTHR THAN FROM CU'],
#     "AMINOETHYLETHANOLAMINE (AEEA)" : ['AMINOETHYLETHANOLAMINE (AEEA)', 'AEEA', 'AMINOETHYLETHANOLAMINE', 'TRIETHANOLAMINE AND ITS SA // AMINOETHYLETHANOLAMINE (AEEA)'],
#     "BHMT" : ['BHMT', 'Bis(hexamethylene)triamine', 'Bis(HexaMethylene Triamine Penta (Methylene Phosphonic Acid))'],
#     "TITANIUM DIOXIDE (PIGMNT/PREP W/=>80%TITNIM DIXD)" : ['TITANIUM DIOXIDE', 'TITANIUM OXIDES'],
#     "Magnesium Nitrate Hexahydrate" : ['Magnesium Nitrate Hexahydrate', 'magnesium dinitrate hexahydrate', 'magnesium(II) nitrate 6-water'],
#     "Rheology Modifier" : ['Rheology Modifier', 'Rheology Modifier (CEMENTS, MORTARS OR CONCRETES)'],
#     "TALLOW DIAMINE ETHOXYLATE (SURFACE ACTIVE,ORG,NT ION)" : ['TALLOW DIAMINE ETHOXYLATE (SURFACE ACTIVE,ORG,NT ION)'],
#     "PARAFORMALDEHYDE" : ['PARAFORMALDEHYDE'],
#     "ACETIC ACID" : ['ACETIC ACID']
# }

# # ORIGINAL: Not present in original code
# # MODIFIED: Added from volza_query_engine to support chemical name variations persistence
# CHEMICAL_VARIATIONS_FILE = "chemical_name_variations.json"

# # ORIGINAL: Not present in original code
# # MODIFIED: Added from volza_query_engine to manage chemical name variations
# class ChemicalVariationsManager:
#     """
#     Manages chemical name variations, loading from and saving to a local file
#     """
#     def __init__(self, file_path=CHEMICAL_VARIATIONS_FILE, initial_data=None):
#         self.file_path = file_path
#         self.variations = initial_data if initial_data else {}
#         self.load_variations()

#     def load_variations(self):
#         """Load chemical name variations from file if it exists"""
#         try:
#             if os.path.exists(self.file_path):
#                 with open(self.file_path, 'r') as f:
#                     loaded_data = json.load(f)
#                     # Update the variations dictionary with loaded data
#                     self.variations.update(loaded_data)
#                 logger.info(f"Loaded chemical variations from {self.file_path}")
#             else:
#                 logger.info(f"No existing variations file found at {self.file_path}")
#                 # Save the initial data if file doesn't exist
#                 self.save_variations()
#         except Exception as e:
#             logger.error(f"Error loading chemical variations: {e}")

#     def save_variations(self):
#         """Save chemical name variations to file"""
#         try:
#             with open(self.file_path, 'w') as f:
#                 json.dump(self.variations, f, indent=4)
#             logger.info(f"Saved chemical variations to {self.file_path}")
#         except Exception as e:
#             logger.error(f"Error saving chemical variations: {e}")

#     def get_variations(self, chemical_name):
#         """
#         Get variations for a chemical name, generating new ones if needed

#         Args:
#             chemical_name (str): Name of the chemical

#         Returns:
#             list: List of variations for the chemical name
#         """
#         # If variations exist for this chemical name, return them
#         if chemical_name in self.variations:
#             logger.info(f"Using cached variations for {chemical_name}")
#             return self.variations[chemical_name]

#         # Otherwise, generate new variations using Perplexity API
#         try:
#             logger.info(f"Generating new variations for {chemical_name}")
#             perplexity_client = PerplexityClient()
#             synonyms = self._get_synonyms_from_perplexity(chemical_name, perplexity_client)

#             # Add the original chemical name if not already in the list
#             if chemical_name not in synonyms:
#                 synonyms.append(chemical_name)

#             # Update the variations dictionary and save to file
#             self.variations[chemical_name] = synonyms
#             self.save_variations()

#             return synonyms
#         except Exception as e:
#             logger.error(f"Error generating variations for {chemical_name}: {e}")
#             # Return just the chemical name if we fail to generate variations
#             return [chemical_name]

#     def _get_synonyms_from_perplexity(self, chemical_name, perplexity_client):
#         """
#         Get synonyms for a chemical name using Perplexity API

#         Args:
#             chemical_name (str): Name of the chemical
#             perplexity_client: Instance of PerplexityClient

#         Returns:
#             list: List of synonyms for the chemical name
#         """
#         # Prepare system prompt for Perplexity
#         system_prompt = """You are a specialized chemical nomenclature expert. Generate top 5 popular synonym variations,
#         abbreviations, and alternative names for the given chemical compound. Return ONLY a JSON array of strings
#         containing all variations."""

#         # Format examples for few-shot learning
#         examples_text = ""
#         examples = list(chemical_name_variations.items())[:3]
#         for i, (original, variations) in enumerate(examples):
#             examples_text += f'Example {i+1}:\nInput: "{original}"\nOutput: {json.dumps(variations)}\n\n'

#         # Create the full prompt
#         prompt = f"""You are a specialized chemical nomenclature expert. Generate top 5 popular synonym variations,
#         abbreviations, and alternative names for the given chemical compound. Return ONLY a JSON array of strings
#         containing all variations.

#         Here are some examples of how you should respond:

#         {examples_text}

#         Now, generate similar variations for the following chemical:
#         Input: "{chemical_name}"
#         Output:"""

#         # Query Perplexity API
#         try:
#             result = perplexity_client.query({
#                 "model": "sonar-pro",  # Or another appropriate Perplexity model
#                 "messages": [
#                     {"role": "system", "content": "You are a specialized chemical nomenclature expert that produces JSON arrays of chemical name variations. Return ONLY a valid JSON array with no additional explanations."},
#                     {"role": "user", "content": prompt}
#                 ],
#                 "temperature": 0.2
#             })

#             # Extract content from Perplexity response
#             if result and "choices" in result and len(result["choices"]) > 0:
#                 content = result["choices"][0]["message"]["content"]

#                 # Try to parse JSON response
#                 try:
#                     # First, try to parse as is - it might be a valid JSON array already
#                     try:
#                         parsed_result = json.loads(content)
#                         if isinstance(parsed_result, list):
#                             return parsed_result
#                     except:
#                         pass

#                     # If that fails, look for JSON array in the text
#                     json_array_pattern = r'\[.*?\]'
#                     json_match = re.search(json_array_pattern, content, re.DOTALL)

#                     if json_match:
#                         json_str = json_match.group(0)
#                         parsed_result = json.loads(json_str)
#                         if isinstance(parsed_result, list):
#                             return parsed_result

#                     # If all parsing attempts fail, return the original name
#                     logger.warning(f"Could not parse JSON from response for '{chemical_name}': {content}")
#                     return [chemical_name]

#                 except Exception as e:
#                     logger.error(f"Error parsing response: {str(e)}")
#                     logger.debug(f"Response content: {content}")
#                     return [chemical_name]

#             return [chemical_name]  # Return original name if there's an error

#         except Exception as e:
#             logger.error(f"Error querying Perplexity API: {str(e)}")
#             return [chemical_name]  # Return original name if there's an error

# # Initialize the chemical variations manager with the initial data
# variations_manager = ChemicalVariationsManager(initial_data=chemical_name_variations)

# class TradeFinderService:
#     def __init__(self):
#         # ORIGINAL: No initialization
#         # MODIFIED: Added tariff API URL from volza_query_engine
#         self.TARIFF_API_URL = "http://13.201.135.9:8082/tariff"

#     def get_db_connection(self):
#         """Get a database connection from the pool or create a new one"""
#         try:
#             logger.debug(f"Going to connect to database: {DB_CONFIG}")
#             conn = psycopg2.connect(**DB_CONFIG)
#             return conn
#         except Exception as e:
#             logger.error(f"Error connecting to database: {e}")
#             raise

#     def get_top_suppliers_by_geography(self,
#                                      hs_code: str,
#                                      months: int,
#                                      destination: str,
#                                      chemical_name: str = None) -> Dict[str, Any]:
#         """Get top geographies supplying products"""
#         query, params = self._build_query_1(hs_code, destination, months, chemical_name)
#         return self._execute_query(query, params)

#     # ORIGINAL: Not present in original code
#     # MODIFIED: Added from volza_query_engine to support tariff data
#     def get_top_suppliers_by_geography_tariff(self,
#                                            hs_code: str,
#                                            months: int,
#                                            destination: str,
#                                            chemical_name: str = None) -> Dict[str, Any]:
#         """Get top geographies supplying products with tariff data"""
#         # Get supplier info
#         result = self.get_top_suppliers_by_geography(hs_code, months, destination, chemical_name)

#         # Augment with tariff data for each country
#         for row in result.get('data', []):
#             country = row.get('origin_country')
#             tariff_info = self._fetch_tariff_data(hs_code, country)
#             row['tariff_info'] = tariff_info

#         return result

#     # ORIGINAL: Not present in original code
#     # MODIFIED: Added from volza_query_engine to fetch tariff data
#     def _fetch_tariff_data(self, hs_code: str, country: str) -> Dict[str, Any]:
#         """Fetch tariff data from the tariff API"""
#         try:
#             response = requests.get(
#                 self.TARIFF_API_URL,
#                 params={"hs_code": hs_code, "country": country},
#                 timeout=5
#             )
#             if response.status_code == 200:
#                 return response.json()
#             else:
#                 return {"error": f"Tariff API error {response.status_code}"}
#         except Exception as e:
#             return {"error": f"Exception in tariff API call: {str(e)}"}

#     def get_trade_data_by_country(self,
#                                  hs_code: str,
#                                  origin: str,
#                                  destination: str,
#                                  months: int) -> Dict[str, Any]:
#         """Get trade data for specific countries"""
#         query, params = self._build_query_2(hs_code, origin, destination, months)
#         return self._execute_query(query, params)

#     def get_top_suppliers(self,
#                          hs_code: str,
#                          origin: str,
#                          destination: str,
#                          months: int,
#                          chemical_name: str = None) -> Dict[str, Any]:
#         """
#         Get top suppliers

#         Args:
#             hs_code: HS code
#             origin: Origin country
#             destination: Destination country
#             months: Number of months to look back
#             chemical_name: Optional chemical name to filter by

#         Returns:
#             Dictionary with query results
#         """
#         # ORIGINAL: Only used _build_query_3 without chemical_name
#         # MODIFIED: Added support for chemical_name parameter from volza_query_engine
#         query, params = self._build_query_3(hs_code, origin, destination, months, chemical_name)
#         return self._execute_query(query, params)

#     def _execute_query(self, query, params):
#         """
#         Execute a query against a PostgreSQL RDS database

#         Args:
#             query (str): SQL query to execute
#             params (Any): Parameters for the query (tuple, list, or dict)

#         Returns:
#             dict: Results in a structured format
#         """
#         logger.debug(f"_execute_query(self, query, params): {query} and {params}")
#         connection = None
#         cursor = None

#         try:
#             # Get a connection
#             connection = self.get_db_connection()
#             logger.debug(f"estabilishing connection: {connection}")
#             cursor = connection.cursor()

#             # Execute query with timing
#             start_time = time.time()
#             logger.debug(f"Execute query with timing: {start_time}")

#             logger.debug(f"query: {query} and params: {params}")

#             cursor.execute(query, params)
#             query_time = time.time() - start_time

#             # Get column names and results
#             column_names = [desc[0] for desc in cursor.description] if cursor.description else []
#             results = cursor.fetchall() if cursor.description else []

#             # Format results as a list of dictionaries
#             formatted_results = []
#             for row in results:
#                 formatted_results.append(dict(zip(column_names, row)))

#             return {
#                 "metadata": {
#                     "row_count": len(formatted_results),
#                     "column_count": len(column_names),
#                     "columns": column_names,
#                     "query_time_ms": round(query_time * 1000, 2)
#                 },
#                 "data": formatted_results
#             }

#         except Exception as e:
#             logger.error(f"Database error: {e}")
#             if connection:
#                 connection.rollback()
#             raise Exception(f"Database error: {e}")

#         finally:
#             # Close resources
#             if cursor:
#                 cursor.close()
#             if connection:
#                 connection.close()

#     # ORIGINAL: Helper method for building product description condition was not present
#     # MODIFIED: Added from volza_query_engine to support chemical name variations
#     def _build_product_desc_condition(self, chemical_name, params_list):
#         """
#         Build a SQL condition for product description matching based on chemical name variations

#         Args:
#             chemical_name (str): Name of the chemical
#             params_list (list): List to append parameters to

#         Returns:
#             tuple: (SQL condition string, updated params list)
#         """
#         product_desc_condition = ""

#         if chemical_name:
#             # Get variations using the manager, which will handle caching and API calls
#             variations = variations_manager.get_variations(chemical_name)

#             # Build the LIKE conditions for product description
#             like_conditions = []
#             for variation in variations:
#                 like_conditions.append("product_desc ILIKE %s")
#                 # Ensure proper pattern matching with % wildcards
#                 search_term = variation
#                 if not search_term.startswith('%'):
#                     search_term = '%' + search_term
#                 if not search_term.endswith('%'):
#                     search_term = search_term + '%'
#                 params_list.append(search_term)

#             if like_conditions:
#                 product_desc_condition = "AND (" + " OR ".join(like_conditions) + ")"

#         return product_desc_condition, params_list

#     def _build_query_1(self, hs_code, destination, last_k_months=12, chemical_name=None):
#         """
#         Build query to retrieve top geographies supplying products

#         Args:
#             hs_code (str): HS code (will use first 4 digits)
#             last_k_months (int): Number of months to look back
#             destination (str): Destination country code (parent_cod)
#             chemical_name (str): Name of the chemical to search for in the variations dict

#         Returns:
#             str: SQL query and parameters
#         """
#         # Extract first 4 digits of HS code
#         hs_code_4digit = hs_code[:4] if len(hs_code) >= 4 else hs_code

#         # Initialize parameters with required values
#         params = [destination, last_k_months]

#         # ORIGINAL: Used static dictionary lookup for chemical name variations
#         # MODIFIED: Now uses the ChemicalVariationsManager to get variations dynamically
#         product_desc_condition = ""
#         if chemical_name:
#             product_desc_condition, params = self._build_product_desc_condition(chemical_name, params)

#         # Add hs_code parameter after the product description parameters
#         params.append(hs_code_4digit)

#         # ORIGINAL: Used 500 as minimum quantity threshold
#         # MODIFIED: Changed to 10 to match volza_query_engine implementation
#         query = f"""
#         WITH filtered_by_hs AS (
#             SELECT
#                 parent_coo,
#                 global_std_unit_id,
#                 std_qty,
#                 fob_value_usd,
#                 date,
#                 product_desc,
#                 gross_wt,
#                 hs_code
#             FROM
#                 volza_trade_data
#             WHERE
#                 parent_cod = %s
#                 AND date >= CURRENT_DATE - INTERVAL '%s month'
#                 {product_desc_condition}
#                 AND SUBSTRING(hs_code, 1, 4) = %s
#         ),
#         aggregated AS (
#             SELECT
#                 parent_coo AS origin_country,
#                 global_std_unit_id,
#                 SUM(std_qty) AS total_quantity,
#                 AVG(fob_value_usd) AS average_fob,
#                 MIN(fob_value_usd) AS minimum_fob,
#                 COUNT(*) AS shipment_count,
#                 SUM(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE 0 END) AS total_gross_weight,
#                 AVG(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE NULL END) AS average_gross_weight
#             FROM
#                 filtered_by_hs
#             WHERE
#                 CASE
#                     -- When gross_wt is a valid number and std_qty exists, use the maximum of both
#                     WHEN gross_wt ~ '^[0-9]+$' AND std_qty IS NOT NULL THEN
#                         GREATEST(gross_wt::INTEGER, std_qty) >= 10
#                     -- When only gross_wt is valid, use it
#                     WHEN gross_wt ~ '^[0-9]+$' THEN
#                         gross_wt::INTEGER >= 10
#                     -- When only std_qty exists, use it
#                     WHEN std_qty IS NOT NULL THEN
#                         std_qty >= 10
#                     -- If neither is valid, exclude the row
#                     ELSE FALSE
#                 END
#             GROUP BY
#                 parent_coo, global_std_unit_id
#             HAVING
#                 COUNT(*) >= 1
#             ORDER BY
#                 total_quantity DESC
#         ),
#         ranked AS (
#             SELECT *,
#                 ROW_NUMBER() OVER (PARTITION BY origin_country ORDER BY total_quantity DESC) AS rn
#             FROM aggregated
#         )

#         SELECT *
#         FROM ranked
#         WHERE rn = 1
#         ORDER BY total_quantity DESC;
#         """

#         return query, tuple(params)

#     def _build_query_2(self, hs_code, origin_country, destination, last_k_months):
#         """
#         Build query to retrieve trade data for a specific HS code, origin country, and destination

#         Args:
#             hs_code (str): HS code (will use first 6 digits)
#             origin_country (str): Origin country code (parent_coo)
#             last_k_months (int): Number of months to look back
#             destination (str): Destination country code (parent_cod)

#         Returns:
#             str: SQL query and parameters
#         """
#         # Extract first 6 digits of HS code
#         hs_code_6digit = hs_code[:6] if len(hs_code) > 6 else hs_code

#         query = """
#         SELECT
#             SUBSTRING(hs_code, 1, 6) AS hs_code_6digit,
#             global_std_unit_id,
#             SUM(std_qty) AS total_quantity,
#             AVG(fob_value_usd) AS average_fob,
#             MIN(fob_value_usd) AS minimum_fob,
#             MAX(fob_value_usd) AS maximum_fob,
#             COUNT(*) AS shipment_count,
#             MIN(date) AS earliest_date,
#             MAX(date) AS latest_date
#         FROM
#             volza_trade_data
#         WHERE
#             SUBSTRING(hs_code, 1, 6) = %s
#             AND parent_coo = %s
#             AND parent_cod = %s
#             AND date >= CURRENT_DATE - INTERVAL '%s month'
#         GROUP BY
#             SUBSTRING(hs_code, 1, 6), global_std_unit_id
#         ORDER BY
#             total_quantity DESC
#         """

#         return query, (hs_code_6digit, origin_country, destination, last_k_months)

#     def _build_query_3(self, hs_code, origin, destination, last_k_months, chemical_name=None):
#         """
#         Build query to retrieve top 10 suppliers for a specific HS code, origin, and destination
#         with optional chemical name filtering

#         Args:
#             hs_code (str): HS code (will use first 6 digits)
#             origin (str): Origin country code (parent_coo)
#             destination (str): Destination country code (parent_cod)
#             last_k_months (int): Number of months to look back
#             chemical_name (str, optional): Name of the chemical to search for

#         Returns:
#             tuple: (SQL query, parameters)
#         """
#         # ORIGINAL: Used first 6 digits of HS code
#         # MODIFIED: Changed to use first 4 digits to match volza_query_engine implementation
#         hs_code_4digit = hs_code[:4] if len(hs_code) >= 4 else hs_code

#         # Initialize parameters list
#         params = [hs_code_4digit, origin, destination, last_k_months]

#         # ORIGINAL: No chemical name filtering
#         # MODIFIED: Added chemical name filtering from volza_query_engine
#         product_desc_condition = ""
#         if chemical_name:
#             product_desc_condition, params = self._build_product_desc_condition(chemical_name, params)

#         # ORIGINAL: Limited to 5 results
#         # MODIFIED: Changed to 10 to match volza_query_engine implementation
#         query = f"""
#         SELECT
#             exporter_name,
#             global_exporter_id,
#             global_std_unit_id,
#             SUM(std_qty) AS total_quantity,
#             AVG(std_qty) AS average_quantity_per_shipment,
#             AVG(fob_value_usd) AS average_fob,
#             MIN(fob_value_usd) AS minimum_fob,
#             COUNT(*) AS shipment_count
#         FROM
#             volza_trade_data
#         WHERE
#             SUBSTRING(hs_code, 1, 4) = %s
#             AND parent_coo = %s
#             AND parent_cod = %s
#             AND date >= CURRENT_DATE - INTERVAL '%s month'
#             {product_desc_condition}
#         GROUP BY
#             exporter_name, global_exporter_id, global_std_unit_id
#         ORDER BY
#             total_quantity DESC
#         LIMIT 10
#         """

#         return query, tuple(params)


import logging
import psycopg2
import time
import json
import os
import re
import requests
from pathlib import Path

from typing import Dict, Any, List, Optional

from app import db
from app.config import DB_CONFIG, SUPPRESS_EXPORT_DATA_FOR_NON_US, DEFAULT_EXPORT_DESTINATION
from app.services.perplexity_client import PerplexityClient

logger = logging.getLogger(__name__)

# ORIGINAL: Static dictionary of chemical name variations
# MODIFIED: Enhanced with more variations from volza_query_engine and added file-based persistence
chemical_name_variations = {
    "Epoxy Resin" : ["Epoxide Resin","Epoxide Resins","Epoxy Resins","Epoxy"],
    "MEA Triazine" : ['MEA Triazine', 'Triazine', 'MEATriazine', 'Monoethanolamine Triazine', 'Mono Ethanol Amine Triazine', 'MEA TRIAZINE 78%', 'MEA TRIAZINE 42%', 'MEA TRIAZINE 78% (TRIAZINE RING:OTHER NSPF)'],
    "MEA (MONO ETHANOL AMINE)": ['MEA (MONO ETHANOL AMINE)', 'MEA', 'MONOETHANOLAMINE', 'ethanolamine', '2-aminoethanol', '2-hydroxyethylamine'],
    "MORPHOLINE (HETEROCYCLIC COMPOUND:OTH NSPF)": ['MORPHOLINE (HETEROCYCLIC COMPOUND:OTH NSPF)', 'Morpholine'],
    "Triazine Fungicide (DISINFECTANTS, AROMATIC OR MOD)": ['Triazine Fungicide (DISINFECTANTS, AROMATIC OR MOD)', 'Triazine Fungicide', 'MSTACK HS 7800 - Triazine Fungicide (DISINFECTANTS, AROMATIC OR MOD) - MEA TRIAZINE 78%'],
    "Alkyl Pyridine Quat (APQ)" : ['Alkyl Pyridine Quat (APQ)', 'APQ', 'Alkyl Pyridine Quat', 'Alkyl Pyridine Quat (APQ) - (PYRIDINE AND ITS SALTS)'],
    "Epoxide Resins" : ['Chain Stopped Short Oil Alkyd Resin', 'ALKYD RESINS IN PRIMARY', 'Chain Stopped Short Oil Alkyd Resin (ALKYD RESINS IN PRIMARY FORM)', 'EPOXIDE RESINS', 'ALKYD RESIN WB4868', 'Epoxy Resin','Epoxy Resins'],
    "ACETONE, OTHR THAN FROM CU" : ['ACETONE', 'ACETONE, OTHR THAN FROM CU'],
    "AMINOETHYLETHANOLAMINE (AEEA)" : ['AMINOETHYLETHANOLAMINE (AEEA)', 'AEEA', 'AMINOETHYLETHANOLAMINE', 'TRIETHANOLAMINE AND ITS SA // AMINOETHYLETHANOLAMINE (AEEA)'],
    "BHMT" : ['BHMT', 'Bis(hexamethylene)triamine', 'Bis(HexaMethylene Triamine Penta (Methylene Phosphonic Acid))'],
    "TITANIUM DIOXIDE (PIGMNT/PREP W/=>80%TITNIM DIXD)" : ['TITANIUM DIOXIDE', 'TITANIUM OXIDES'],
    "Magnesium Nitrate Hexahydrate" : ['Magnesium Nitrate Hexahydrate', 'magnesium dinitrate hexahydrate', 'magnesium(II) nitrate 6-water'],
    "Rheology Modifier" : ['Rheology Modifier', 'Rheology Modifier (CEMENTS, MORTARS OR CONCRETES)'],
    "TALLOW DIAMINE ETHOXYLATE (SURFACE ACTIVE,ORG,NT ION)" : ['TALLOW DIAMINE ETHOXYLATE (SURFACE ACTIVE,ORG,NT ION)'],
    "PARAFORMALDEHYDE" : ['PARAFORMALDEHYDE'],
    "ACETIC ACID" : ['ACETIC ACID']
}

# ORIGINAL: Not present in original code
# MODIFIED: Added from volza_query_engine to support chemical name variations persistence
CHEMICAL_VARIATIONS_FILE = "chemical_name_variations.json"

# ORIGINAL: Not present in original code
# MODIFIED: Added from volza_query_engine to manage chemical name variations
class ChemicalVariationsManager:
    """
    Manages chemical name variations, loading from and saving to a local file
    """
    def __init__(self, file_path=CHEMICAL_VARIATIONS_FILE, initial_data=None):
        self.file_path = file_path
        self.variations = initial_data if initial_data else {}
        self.load_variations()

    def load_variations(self):
        """Load chemical name variations from file if it exists"""
        try:
            if os.path.exists(self.file_path):
                with open(self.file_path, 'r') as f:
                    loaded_data = json.load(f)
                    # Update the variations dictionary with loaded data
                    self.variations.update(loaded_data)
                logger.info(f"Loaded chemical variations from {self.file_path}")
            else:
                logger.info(f"No existing variations file found at {self.file_path}")
                # Save the initial data if file doesn't exist
                self.save_variations()
        except Exception as e:
            logger.error(f"Error loading chemical variations: {e}")

    def save_variations(self):
        """Save chemical name variations to file"""
        try:
            with open(self.file_path, 'w') as f:
                json.dump(self.variations, f, indent=4)
            logger.info(f"Saved chemical variations to {self.file_path}")
        except Exception as e:
            logger.error(f"Error saving chemical variations: {e}")

    def get_variations(self, chemical_name):
        """
        Get variations for a chemical name, generating new ones if needed

        Args:
            chemical_name (str): Name of the chemical

        Returns:
            list: List of variations for the chemical name
        """
        # If variations exist for this chemical name, return them
        if chemical_name in self.variations:
            logger.info(f"Using cached variations for {chemical_name}")
            return self.variations[chemical_name]

        # Otherwise, generate new variations using Perplexity API
        try:
            logger.info(f"Generating new variations for {chemical_name}")
            perplexity_client = PerplexityClient()
            synonyms = self._get_synonyms_from_perplexity(chemical_name, perplexity_client)

            # Add the original chemical name if not already in the list
            if chemical_name not in synonyms:
                synonyms.append(chemical_name)

            # Update the variations dictionary and save to file
            self.variations[chemical_name] = synonyms
            self.save_variations()

            return synonyms
        except Exception as e:
            logger.error(f"Error generating variations for {chemical_name}: {e}")
            # Return just the chemical name if we fail to generate variations
            return [chemical_name]

    def _get_synonyms_from_perplexity(self, chemical_name, perplexity_client):
        """
        Get synonyms for a chemical name using Perplexity API

        Args:
            chemical_name (str): Name of the chemical
            perplexity_client: Instance of PerplexityClient

        Returns:
            list: List of synonyms for the chemical name
        """
        # Prepare system prompt for Perplexity
        system_prompt = """You are a specialized chemical nomenclature expert. Generate top 5 popular synonym variations,
        abbreviations, and alternative names for the given chemical compound. Return ONLY a JSON array of strings
        containing all variations."""

        # Format examples for few-shot learning
        examples_text = ""
        examples = list(chemical_name_variations.items())[:3]
        for i, (original, variations) in enumerate(examples):
            examples_text += f'Example {i+1}:\nInput: "{original}"\nOutput: {json.dumps(variations)}\n\n'

        # Create the full prompt
        prompt = f"""You are a specialized chemical nomenclature expert. Generate top 5 popular synonym variations,
        abbreviations, and alternative names for the given chemical compound. Return ONLY a JSON array of strings
        containing all variations.

        Here are some examples of how you should respond:

        {examples_text}

        Now, generate similar variations for the following chemical:
        Input: "{chemical_name}"
        Output:"""

        # Query Perplexity API
        try:
            result = perplexity_client.query({
                "model": "sonar-pro",  # Or another appropriate Perplexity model
                "messages": [
                    {"role": "system", "content": "You are a specialized chemical nomenclature expert that produces JSON arrays of chemical name variations. Return ONLY a valid JSON array with no additional explanations."},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.2
            })

            # Extract content from Perplexity response
            if result and "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]

                # Try to parse JSON response
                try:
                    # First, try to parse as is - it might be a valid JSON array already
                    try:
                        parsed_result = json.loads(content)
                        if isinstance(parsed_result, list):
                            return parsed_result
                    except:
                        pass

                    # If that fails, look for JSON array in the text
                    json_array_pattern = r'\[.*?\]'
                    json_match = re.search(json_array_pattern, content, re.DOTALL)

                    if json_match:
                        json_str = json_match.group(0)
                        parsed_result = json.loads(json_str)
                        if isinstance(parsed_result, list):
                            return parsed_result

                    # If all parsing attempts fail, return the original name
                    logger.warning(f"Could not parse JSON from response for '{chemical_name}': {content}")
                    return [chemical_name]

                except Exception as e:
                    logger.error(f"Error parsing response: {str(e)}")
                    logger.debug(f"Response content: {content}")
                    return [chemical_name]

            return [chemical_name]  # Return original name if there's an error

        except Exception as e:
            logger.error(f"Error querying Perplexity API: {str(e)}")
            return [chemical_name]  # Return original name if there's an error

# Initialize the chemical variations manager with the initial data
variations_manager = ChemicalVariationsManager(initial_data=chemical_name_variations)

def is_us_destination(destination: str) -> bool:
    """
    Check if the destination country is the United States

    Args:
        destination: Destination country name or code

    Returns:
        bool: True if destination is US, False otherwise
    """
    if not destination:
        return False

    destination_lower = destination.lower().strip()
    us_identifiers = [
        'united states', 'usa', 'us', 'america', 'united states of america'
    ]

    return destination_lower in us_identifiers

def should_suppress_export_data(destination: str) -> bool:
    """
    Determine if export data should be suppressed for the given destination

    Args:
        destination: Destination country name or code

    Returns:
        bool: True if export data should be suppressed, False otherwise
    """
    if not SUPPRESS_EXPORT_DATA_FOR_NON_US:
        return False

    return not is_us_destination(destination)

def get_effective_destination(destination: str) -> str:
    """
    Get the effective destination for data queries.
    If export data should be suppressed for non-US destinations, return US as default.

    Args:
        destination: Original destination country

    Returns:
        str: Effective destination to use for queries
    """
    if should_suppress_export_data(destination):
        logger.info(f"Suppressing export data for non-US destination '{destination}', using '{DEFAULT_EXPORT_DESTINATION}' instead")
        return DEFAULT_EXPORT_DESTINATION

    return destination

class TradeFinderService:
    def __init__(self):
        # ORIGINAL: No initialization
        # MODIFIED: Added tariff API URL from volza_query_engine
        self.TARIFF_API_URL = "http://13.201.135.9:8082/tariff"

        # Import configuration for HTS2Duty integration
        from app.config import USE_LOCAL_HTS2DUTY
        self.use_local_hts2duty = USE_LOCAL_HTS2DUTY

        # Note: Database and LLM client functionality has been integrated into controllers

    def _add_export_suppression_metadata(self, result: Dict[str, Any], original_destination: str, effective_destination: str) -> Dict[str, Any]:
        """
        Add metadata about export data suppression to the result

        Args:
            result: Query result dictionary
            original_destination: Original destination requested
            effective_destination: Effective destination used for query

        Returns:
            Updated result with suppression metadata
        """
        if original_destination != effective_destination:
            if 'metadata' not in result:
                result['metadata'] = {}

            result['metadata']['export_data_suppressed'] = True
            result['metadata']['original_destination'] = original_destination
            result['metadata']['effective_destination'] = effective_destination
            result['metadata']['suppression_reason'] = f"Export data suppressed for non-US destination '{original_destination}'. Showing US data instead."
        else:
            if 'metadata' not in result:
                result['metadata'] = {}
            result['metadata']['export_data_suppressed'] = False

        return result

    def get_db_connection(self):
        """Get a database connection from the pool or create a new one"""
        try:
            logger.debug(f"Going to connect to database: {DB_CONFIG}")
            conn = psycopg2.connect(**DB_CONFIG)
            return conn
        except Exception as e:
            logger.error(f"Error connecting to database: {e}")
            raise

    def get_top_suppliers_by_geography(self,
                                     hs_code: str,
                                     months: int,
                                     destination: str,
                                     chemical_name: str = None) -> Dict[str, Any]:
        """Get top geographies supplying products with optional chemical name filtering"""
        # Apply destination filtering logic
        effective_destination = get_effective_destination(destination)
        original_destination = destination

        # First try with chemical name filtering if provided
        if chemical_name:
            query, params = self._build_query_1(hs_code, effective_destination, months, chemical_name)
            result = self._execute_query(query, params)

            # If we get results, return them
            if result.get('data') and len(result['data']) > 0:
                logger.info(f"Found {len(result['data'])} results with chemical name filtering for '{chemical_name}'")
                result['fallback_used'] = False
                result = self._add_export_suppression_metadata(result, original_destination, effective_destination)
                return result
            else:
                logger.warning(f"No results found with chemical name filtering for '{chemical_name}'. Falling back to query without chemical name.")
                # Fall back to query without chemical name
                query, params = self._build_query_1(hs_code, effective_destination, months, None)
                result = self._execute_query(query, params)
                result['fallback_used'] = True
                logger.info(f"Fallback query returned {len(result.get('data', []))} results")
                result = self._add_export_suppression_metadata(result, original_destination, effective_destination)
                return result
        else:
            # No chemical name provided, run normal query
            query, params = self._build_query_1(hs_code, effective_destination, months, chemical_name)
            result = self._execute_query(query, params)
            result = self._add_export_suppression_metadata(result, original_destination, effective_destination)
            return result

    # ORIGINAL: Not present in original code
    # MODIFIED: Added from volza_query_engine to support tariff data
    def get_top_suppliers_by_geography_tariff(self,
                                           hs_code: str,
                                           months: int,
                                           destination: str,
                                           chemical_name: str = None) -> Dict[str, Any]:
        """Get top geographies supplying products with tariff data"""
        import json

        # Apply destination filtering logic
        effective_destination = get_effective_destination(destination)
        original_destination = destination

        # Get supplier info using effective destination
        result = self.get_top_suppliers_by_geography(hs_code, months, effective_destination, chemical_name)

        # Augment with tariff data for each country
        for row in result.get('data', []):
            country = row.get('origin_country')
            tariff_info = self._fetch_tariff_data(hs_code, country)
            row['tariff_info'] = tariff_info

        # Apply new sorting: average_fob descending, then total_duty ascending
        data = result.get('data', [])
        if data:
            # Extract total_duty from tariff_info for sorting
            for row in data:
                try:
                    tariff_info = row.get('tariff_info', {})
                    if isinstance(tariff_info, str):
                        tariff_data = json.loads(tariff_info)
                    else:
                        tariff_data = tariff_info

                    total_duty_str = tariff_data.get('Total Duty', '0%')
                    # Extract numeric value from percentage string (e.g., "6.1%" -> 6.1)
                    total_duty_numeric = float(total_duty_str.replace('%', '')) if total_duty_str.replace('%', '').replace('.', '').isdigit() else 0.0
                    row['_total_duty_numeric'] = total_duty_numeric
                except (json.JSONDecodeError, ValueError, AttributeError) as e:
                    logger.warning(f"Error parsing tariff info for {row.get('origin_country', 'unknown')}: {e}")
                    row['_total_duty_numeric'] = 0.0

            # Sort by average_fob descending, then by total_duty ascending
            data.sort(key=lambda x: (-x.get('average_fob', 0), x.get('_total_duty_numeric', 0)))

            # Remove the temporary sorting field
            for row in data:
                row.pop('_total_duty_numeric', None)

            result['data'] = data

        # Add export suppression metadata
        result = self._add_export_suppression_metadata(result, original_destination, effective_destination)
        return result

    # ORIGINAL: Not present in original code
    # MODIFIED: Added from volza_query_engine to fetch tariff data
    def _fetch_tariff_data(self, hs_code: str, country: str) -> Dict[str, Any]:
        """
        Fetch tariff data for a given HS code and country.
        Uses local HTS2Duty service if enabled, otherwise falls back to external API.
        """
        try:
            # Use local HTS2Duty service if enabled
            if self.use_local_hts2duty:
                logger.debug("Using local HTS2Duty service for tariff data")
                from app.services.hts2duty_service import get_hts2duty_service
                hts2duty_service = get_hts2duty_service()
                tariff_data = hts2duty_service.get_tariff_data(hs_code, country)

                # Check if there was an error in the response
                if 'error' in tariff_data:
                    logger.warning(f"HTS2Duty service error for {country}: {tariff_data['error']}")
                    # Fall back to external API if local service fails
                    return self._fetch_external_tariff_data(hs_code, country)

                logger.debug(f"Received tariff data from HTS2Duty for {country}")
                return tariff_data
            else:
                # Use external API
                return self._fetch_external_tariff_data(hs_code, country)

        except Exception as e:
            logger.error(f"Error in _fetch_tariff_data for {country}: {str(e)}")
            # Try external API as fallback if local service fails
            if self.use_local_hts2duty:
                logger.info(f"Falling back to external API for {country}")
                try:
                    return self._fetch_external_tariff_data(hs_code, country)
                except Exception as fallback_error:
                    logger.error(f"External API fallback also failed for {country}: {str(fallback_error)}")
            return {"error": f"Exception in tariff API call: {str(e)}"}

    def _fetch_external_tariff_data(self, hs_code: str, country: str) -> Dict[str, Any]:
        """Fetch tariff data from external API"""
        try:
            response = requests.get(
                self.TARIFF_API_URL,
                params={"hs_code": hs_code, "country": country},
                timeout=5
            )
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Tariff API error {response.status_code}"}
        except Exception as e:
            return {"error": f"Exception in external tariff API call: {str(e)}"}

    def get_trade_data_by_country(self,
                                 hs_code: str,
                                 origin: str,
                                 destination: str,
                                 months: int) -> Dict[str, Any]:
        """Get trade data for specific countries"""
        # Apply destination filtering logic
        effective_destination = get_effective_destination(destination)
        original_destination = destination

        query, params = self._build_query_2(hs_code, origin, effective_destination, months)
        result = self._execute_query(query, params)
        result = self._add_export_suppression_metadata(result, original_destination, effective_destination)
        return result

    def get_top_suppliers(self,
                         hs_code: str,
                         origin: str,
                         destination: str,
                         months: int,
                         chemical_name: str = None) -> Dict[str, Any]:
        """
        Get top suppliers with optional chemical name filtering

        Args:
            hs_code: HS code
            origin: Origin country
            destination: Destination country
            months: Number of months to look back
            chemical_name: Optional chemical name to filter by

        Returns:
            Dictionary with query results
        """
        # Apply destination filtering logic
        effective_destination = get_effective_destination(destination)
        original_destination = destination

        # First try with chemical name filtering if provided
        if chemical_name:
            query, params = self._build_query_3(hs_code, origin, effective_destination, months, chemical_name)
            result = self._execute_query(query, params)

            # If we get results, return them
            if result.get('data') and len(result['data']) > 0:
                logger.info(f"Found {len(result['data'])} suppliers with chemical name filtering for '{chemical_name}'")
                result['fallback_used'] = False
                result = self._add_export_suppression_metadata(result, original_destination, effective_destination)
                return result
            else:
                logger.warning(f"No suppliers found with chemical name filtering for '{chemical_name}'. Falling back to query without chemical name.")
                # Fall back to query without chemical name
                query, params = self._build_query_3(hs_code, origin, effective_destination, months, None)
                result = self._execute_query(query, params)
                result['fallback_used'] = True
                logger.info(f"Fallback query returned {len(result.get('data', []))} suppliers")
                result = self._add_export_suppression_metadata(result, original_destination, effective_destination)
                return result
        else:
            # No chemical name provided, run normal query
            query, params = self._build_query_3(hs_code, origin, effective_destination, months, chemical_name)
            result = self._execute_query(query, params)
            result = self._add_export_suppression_metadata(result, original_destination, effective_destination)
            return result

    def _execute_query(self, query, params):
        """
        Execute a query against a PostgreSQL RDS database

        Args:
            query (str): SQL query to execute
            params (Any): Parameters for the query (tuple, list, or dict)

        Returns:
            dict: Results in a structured format
        """
        logger.debug(f"_execute_query(self, query, params): {query} and {params}")
        connection = None
        cursor = None

        try:
            # Get a connection
            connection = self.get_db_connection()
            logger.debug(f"estabilishing connection: {connection}")
            cursor = connection.cursor()

            # Execute query with timing
            start_time = time.time()
            logger.debug(f"Execute query with timing: {start_time}")

            logger.debug(f"query: {query} and params: {params}")

            cursor.execute(query, params)
            query_time = time.time() - start_time

            # Get column names and results
            column_names = [desc[0] for desc in cursor.description] if cursor.description else []
            results = cursor.fetchall() if cursor.description else []

            # Format results as a list of dictionaries
            formatted_results = []
            for row in results:
                formatted_results.append(dict(zip(column_names, row)))

            return {
                "metadata": {
                    "row_count": len(formatted_results),
                    "column_count": len(column_names),
                    "columns": column_names,
                    "query_time_ms": round(query_time * 1000, 2)
                },
                "data": formatted_results
            }

        except Exception as e:
            logger.error(f"Database error: {e}")
            if connection:
                connection.rollback()
            raise Exception(f"Database error: {e}")

        finally:
            # Close resources
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    # ORIGINAL: Helper method for building product description condition was not present
    # MODIFIED: Added from volza_query_engine to support chemical name variations
    def _build_product_desc_condition(self, chemical_name, params_list):
        """
        Build a SQL condition for product description matching based on chemical name variations

        Args:
            chemical_name (str): Name of the chemical
            params_list (list): List to append parameters to

        Returns:
            tuple: (SQL condition string, updated params list)
        """
        product_desc_condition = ""

        if chemical_name:
            # Get variations using the manager, which will handle caching and API calls
            variations = variations_manager.get_variations(chemical_name)

            # Build the LIKE conditions for product description
            like_conditions = []
            for variation in variations:
                like_conditions.append("product_desc ILIKE %s")
                # Ensure proper pattern matching with % wildcards
                search_term = variation
                if not search_term.startswith('%'):
                    search_term = '%' + search_term
                if not search_term.endswith('%'):
                    search_term = search_term + '%'
                params_list.append(search_term)

            if like_conditions:
                product_desc_condition = "AND (" + " OR ".join(like_conditions) + ")"

        return product_desc_condition, params_list

    def _build_query_1(self, hs_code, destination, last_k_months=12, chemical_name=None):
        """
        Build query to retrieve top geographies supplying products

        Args:
            hs_code (str): HS code (will use first 4 digits)
            last_k_months (int): Number of months to look back
            destination (str): Destination country code (parent_cod)
            chemical_name (str): Name of the chemical to search for in the variations dict

        Returns:
            str: SQL query and parameters
        """
        # Extract first 4 digits of HS code
        hs_code_4digit = hs_code[:4] if len(hs_code) >= 4 else hs_code

        # Initialize parameters with required values
        params = [destination, last_k_months]

        # ORIGINAL: Used static dictionary lookup for chemical name variations
        # MODIFIED: Now uses the ChemicalVariationsManager to get variations dynamically
        product_desc_condition = ""
        if chemical_name:
            product_desc_condition, params = self._build_product_desc_condition(chemical_name, params)

        # Add hs_code parameter after the product description parameters
        params.append(hs_code_4digit)

        # ORIGINAL: Used 500 as minimum quantity threshold
        # MODIFIED: Changed to 10 to match volza_query_engine implementation
        query = f"""
        WITH filtered_by_hs AS (
            SELECT
                parent_coo,
                global_std_unit_id,
                std_qty,
                fob_value_usd,
                date,
                product_desc,
                gross_wt,
                hs_code
            FROM
                volza_trade_data
            WHERE
                parent_cod = %s
                AND date >= CURRENT_DATE - INTERVAL '%s month'
                {product_desc_condition}
                AND SUBSTRING(hs_code, 1, 4) = %s
        ),
        aggregated AS (
            SELECT
                parent_coo AS origin_country,
                global_std_unit_id,
                SUM(std_qty) AS total_quantity,
                AVG(fob_value_usd) AS average_fob,
                MIN(fob_value_usd) AS minimum_fob,
                COUNT(*) AS shipment_count,
                SUM(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE 0 END) AS total_gross_weight,
                AVG(CASE WHEN gross_wt ~ '^[0-9]+$' THEN gross_wt::NUMERIC ELSE NULL END) AS average_gross_weight
            FROM
                filtered_by_hs
            WHERE
                CASE
                    -- When gross_wt is a valid number and std_qty exists, use the maximum of both
                    WHEN gross_wt ~ '^[0-9]+$' AND std_qty IS NOT NULL THEN
                        GREATEST(gross_wt::INTEGER, std_qty) >= 10
                    -- When only gross_wt is valid, use it
                    WHEN gross_wt ~ '^[0-9]+$' THEN
                        gross_wt::INTEGER >= 10
                    -- When only std_qty exists, use it
                    WHEN std_qty IS NOT NULL THEN
                        std_qty >= 10
                    -- If neither is valid, exclude the row
                    ELSE FALSE
                END
            GROUP BY
                parent_coo, global_std_unit_id
            HAVING
                COUNT(*) >= 1
            ORDER BY
                total_quantity DESC
        ),
        ranked AS (
            SELECT *,
                ROW_NUMBER() OVER (PARTITION BY origin_country ORDER BY total_quantity DESC) AS rn
            FROM aggregated
        )

        SELECT *
        FROM ranked
        WHERE rn = 1;
        """

        return query, tuple(params)

    def _build_query_2(self, hs_code, origin_country, destination, last_k_months):
        """
        Build query to retrieve trade data for a specific HS code, origin country, and destination

        Args:
            hs_code (str): HS code (will use first 6 digits)
            origin_country (str): Origin country code (parent_coo)
            last_k_months (int): Number of months to look back
            destination (str): Destination country code (parent_cod)

        Returns:
            str: SQL query and parameters
        """
        # Extract first 6 digits of HS code
        hs_code_6digit = hs_code[:6] if len(hs_code) > 6 else hs_code

        query = """
        SELECT
            SUBSTRING(hs_code, 1, 6) AS hs_code_6digit,
            global_std_unit_id,
            SUM(std_qty) AS total_quantity,
            AVG(fob_value_usd) AS average_fob,
            MIN(fob_value_usd) AS minimum_fob,
            MAX(fob_value_usd) AS maximum_fob,
            COUNT(*) AS shipment_count,
            MIN(date) AS earliest_date,
            MAX(date) AS latest_date
        FROM
            volza_trade_data
        WHERE
            SUBSTRING(hs_code, 1, 6) = %s
            AND parent_coo = %s
            AND parent_cod = %s
            AND date >= CURRENT_DATE - INTERVAL '%s month'
        GROUP BY
            SUBSTRING(hs_code, 1, 6), global_std_unit_id
        ORDER BY
            total_quantity DESC
        """

        return query, (hs_code_6digit, origin_country, destination, last_k_months)

    def _build_query_3(self, hs_code, origin, destination, last_k_months, chemical_name=None):
        """
        Build query to retrieve top 10 suppliers for a specific HS code, origin, and destination
        with optional chemical name filtering

        Args:
            hs_code (str): HS code (will use first 6 digits)
            origin (str): Origin country code (parent_coo)
            destination (str): Destination country code (parent_cod)
            last_k_months (int): Number of months to look back
            chemical_name (str, optional): Name of the chemical to search for

        Returns:
            tuple: (SQL query, parameters)
        """
        # ORIGINAL: Used first 6 digits of HS code
        # MODIFIED: Changed to use first 4 digits to match volza_query_engine implementation
        hs_code_4digit = hs_code[:4] if len(hs_code) >= 4 else hs_code

        # Initialize parameters list
        params = [hs_code_4digit, origin, destination, last_k_months]

        # ORIGINAL: No chemical name filtering
        # MODIFIED: Added chemical name filtering from volza_query_engine
        product_desc_condition = ""
        if chemical_name:
            product_desc_condition, params = self._build_product_desc_condition(chemical_name, params)

        # ORIGINAL: Limited to 5 results
        # MODIFIED: Changed to 10 to match volza_query_engine implementation
        query = f"""
        SELECT
            exporter_name,
            global_exporter_id,
            global_std_unit_id,
            SUM(std_qty) AS total_quantity,
            AVG(std_qty) AS average_quantity_per_shipment,
            AVG(fob_value_usd) AS average_fob,
            MIN(fob_value_usd) AS minimum_fob,
            COUNT(*) AS shipment_count
        FROM
            volza_trade_data
        WHERE
            SUBSTRING(hs_code, 1, 4) = %s
            AND parent_coo = %s
            AND parent_cod = %s
            AND date >= CURRENT_DATE - INTERVAL '%s month'
            {product_desc_condition}
        GROUP BY
            exporter_name, global_exporter_id, global_std_unit_id
        ORDER BY
            total_quantity DESC
        LIMIT 10
        """

        return query, tuple(params)
