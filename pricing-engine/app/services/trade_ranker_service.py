# import json
# import logging
# import requests

# from typing import List, Dict, Any
# from app.config import TARIFF_SERVICE_BASE_URL

# logger = logging.getLogger(__name__)


# # from app.utils import rank_suppliers, rank_countries, fetch_tariff_data, extract_duty_percentage

# class TradeRankerService:

#     def rank_countries(self, countries, hs_code):
#         """Rank countries based on FOB, shipment count, and tariff data"""
#         enriched_countries = []
#         unique_countries = {}
        
#         for country_data in countries:
#             country_name = country_data.get('origin_country')
#             if not country_name:
#                 continue
                
#             if country_name in unique_countries:
#                 existing = unique_countries[country_name]
#                 existing['shipment_count'] += country_data.get('shipment_count', 0)
#                 if country_data.get('average_fob', 0) > existing.get('average_fob', 0):
#                     existing['average_fob'] = country_data.get('average_fob', 0)
#             else:
#                 unique_countries[country_name] = {
#                     'origin_country': country_name,
#                     'average_fob': country_data.get('average_fob', 0),
#                     'shipment_count': country_data.get('shipment_count', 0)
#                 }
        
#         for country_name, country_data in unique_countries.items():
#             logger.info("Ignoring fetching of tarif as the service might be down or take too long to respond")
#             # try:
#             #     tariff_data = TradeRankerService().fetch_tariff_data(country_name, hs_code)
#             #     duty_str = tariff_data.get('Total Duty', '0%')
#             #     duty_percentage = TradeRankerService().extract_duty_percentage(duty_str)
                
#             #     country_data['Total Duty'] = duty_str
#             #     country_data['duty_percentage'] = duty_percentage
#             #     enriched_countries.append(country_data)
#             # except Exception as e:
#             #     logger.error(f"Failed to fetch tariff data for {country_name}: {str(e)}")
#             country_data['Total Duty'] = 'N/A'
#             country_data['duty_percentage'] = 0.0
#             enriched_countries.append(country_data)
        
#         return TradeRankerService().rank_enriched_countries(enriched_countries)

#     def normalize_values(self, values, higher_is_better=True):
#         """Normalize a list of values to a 0-1 scale."""
#         min_val = min(values)
#         max_val = max(values)
        
#         # Handle the case where all values are the same
#         if min_val == max_val:
#             return [1.0 if higher_is_better else 0.0] * len(values)
        
#         if higher_is_better:
#             return [(val - min_val) / (max_val - min_val) for val in values]
#         else:
#             return [1 - ((val - min_val) / (max_val - min_val)) for val in values]

#     def calculate_composite_scores(self, suppliers):
#         """Calculate composite scores for each supplier using equal weighting."""
        
#         # Calculate unit costs and price consistency
#         unit_costs = []
#         price_consistency = []
#         shipment_counts = []
#         total_quantities = []
        
#         for supplier in suppliers:
#             # Handle edge case: if average_quantity_per_shipment is 0
#             avg_quantity = supplier["average_quantity_per_shipment"]
#             if avg_quantity == 0:
#                 unit_cost = float('inf')  # Assign a very high unit cost
#             else:
#                 unit_cost = supplier["average_fob"] / avg_quantity
            
#             # Handle edge case: if average_fob is 0
#             avg_fob = supplier["average_fob"]
#             if avg_fob == 0:
#                 consistency = 0  # Assign 0 consistency when average FOB is 0
#             else:
#                 consistency = supplier["minimum_fob"] / avg_fob
            
#             unit_costs.append(unit_cost)
#             price_consistency.append(consistency)
#             shipment_counts.append(supplier["shipment_count"])
#             total_quantities.append(supplier["total_quantity"])
        
#         # Normalize all values to 0-1 scale
#         norm_shipment_counts = self.normalize_values(shipment_counts, higher_is_better=True)
#         norm_total_quantities = self.normalize_values(total_quantities, higher_is_better=True)
#         norm_unit_costs = self.normalize_values(unit_costs, higher_is_better=False)
#         norm_price_consistency = self.normalize_values(price_consistency, higher_is_better=True)
        
#         # Calculate composite scores with equal weights (0.25 each)
#         scores = []
#         for i in range(len(suppliers)):
#             score = (
#                 0.25 * norm_shipment_counts[i] +
#                 0.25 * norm_total_quantities[i] +
#                 0.25 * norm_unit_costs[i] +
#                 0.25 * norm_price_consistency[i]
#             )
#             scores.append(score)
        
#         return scores

#     def rank_suppliers(self, suppliers):
#         """Rank suppliers based on composite scores and return top 3 with rank and score."""
        
#         # Calculate scores for all suppliers
#         scores = self.calculate_composite_scores(suppliers)
        
#         # Create a list of (supplier, score) tuples
#         supplier_scores = list(zip(suppliers, scores))
        
#         # Sort by score in descending order
#         sorted_suppliers = sorted(supplier_scores, key=lambda x: x[1], reverse=True)
        
#         # If 3 or fewer suppliers, include all with ranks
#         if len(suppliers) <= 3:
#             ranked_suppliers = []
#             for i, (supplier, score) in enumerate(sorted_suppliers):
#                 # Create a copy of the supplier dict to avoid modifying the original
#                 supplier_with_rank = supplier.copy()
#                 # Add rank (1-based) and score
#                 supplier_with_rank["Rank"] = i + 1
#                 supplier_with_rank["Score"] = round(score, 4)  # Round to 4 decimal places for readability
#                 ranked_suppliers.append(supplier_with_rank)
#             return ranked_suppliers
        
#         # Otherwise, include only top 3 with ranks
#         ranked_suppliers = []
#         for i, (supplier, score) in enumerate(sorted_suppliers[:3]):
#             # Create a copy of the supplier dict to avoid modifying the original
#             supplier_with_rank = supplier.copy()
#             # Add rank (1-based) and score
#             supplier_with_rank["Rank"] = i + 1
#             supplier_with_rank["Score"] = round(score, 4)  # Round to 4 decimal places for readability
#             ranked_suppliers.append(supplier_with_rank)
        
#         return ranked_suppliers

#     def fetch_tariff_data(self, country, hs_code):
#         """
#         Fetch tariff data from external service for a given country and HS code.
        
#         Args:
#             country (str): Country name
#             hs_code (str): HS code
            
#         Returns:
#             dict: Tariff data including Total Duty
#         """
#         logger.debug(f"Fetching tariff data for country: {country}, HS code: {hs_code}")
        
#         try:
#             # Make the request to the external service
#             response = requests.get(f"{TARIFF_SERVICE_BASE_URL}/tariff?country={country}&hs_code={hs_code}")
            
#             # Check if the request was successful
#             if response.status_code == 200:
#                 # The response might be a JSON string inside a JSON response
#                 # First, try to parse as regular JSON
#                 try:
#                     tariff_data = response.json()
                    
#                     # If the result is a string, it might be a JSON string that needs further parsing
#                     if isinstance(tariff_data, str):
#                         tariff_data = json.loads(tariff_data)
                    
#                     logger.debug(f"Received tariff data for {country}: Total Duty = {tariff_data.get('Total Duty', 'N/A')}")
#                     return tariff_data
                    
#                 except json.JSONDecodeError as e:
#                     # If JSON parsing fails, try to extract as plain text and parse manually
#                     logger.error(f"JSON parsing error for {country}: {str(e)}")
                    
#                     # Try to parse the raw response text as JSON
#                     try:
#                         tariff_data = json.loads(response.text)
#                         logger.debug(f"Successfully parsed response text as JSON for {country}")
#                         return tariff_data
#                     except json.JSONDecodeError:
#                         logger.error(f"Failed to parse response for {country} in any format")
#                         raise Exception(f"Invalid response format from tariff API for {country}")
#             else:
#                 logger.error(f"API request failed for {country} with status code {response.status_code}")
#                 raise Exception(f"API request failed with status {response.status_code}")
                
#         except Exception as e:
#             logger.error(f"Error in fetch_tariff_data for {country}: {str(e)}")
#             # Re-raise the exception for the caller to handle
#             raise

#     # Function to extract numerical duty percentage from string (e.g., "6.5%" -> 6.5)
#     def extract_duty_percentage(self, duty_str):
#         """Extract numerical percentage from duty string."""
#         if not duty_str or duty_str == "NA":
#             return 0.0
            
#         # Handle the case where duty_str is already a number
#         if isinstance(duty_str, (int, float)):
#             return float(duty_str)
            
#         # Remove % symbol and convert to float
#         try:
#             return float(duty_str.replace("%", ""))
#         except (ValueError, AttributeError) as e:
#             logger.error(f"Could not parse duty string: {duty_str} - Error: {str(e)}")
#             return 0.0

#     # Function to calculate country scores based on average FOB, shipment count, and duty
#     def calculate_country_scores(self, countries_data):
#         """
#         Calculate composite scores for countries based on:
#         - average_fob (higher is better)
#         - shipment_count (higher is better)
#         - duty_percentage (lower is better)
        
#         With highest weight on duty_percentage.
#         """
#         # Extract values for normalization
#         avg_fobs = [country.get('average_fob', 0) for country in countries_data]
#         shipment_counts = [country.get('shipment_count', 0) for country in countries_data]
#         duty_percentages = [country.get('duty_percentage', 0) for country in countries_data]
        
#         # Normalize values
#         norm_avg_fobs = self.normalize_values(avg_fobs, higher_is_better=True)
#         norm_shipment_counts = self.normalize_values(shipment_counts, higher_is_better=True)
#         norm_duty_percentages = self.normalize_values(duty_percentages, higher_is_better=False)
        
#         # Calculate composite scores with weights (higher weight for duty)
#         # Weight distribution: 20% FOB, 20% shipment count, 60% duty
#         scores = []
#         for i in range(len(countries_data)):
#             score = (
#                 0.20 * norm_avg_fobs[i] +
#                 0.20 * norm_shipment_counts[i] +
#                 0.60 * norm_duty_percentages[i]
#             )
#             scores.append(score)
        
#         return scores

#     # Function to rank countries based on composite scores
#     def rank_enriched_countries(self, countries_data):
#         """Rank countries based on composite scores and return top 3 with rank and score."""
#         # First, ensure each country has all required fields
#         for country in countries_data:
#             if 'duty_percentage' not in country:
#                 logger.warning(f"Missing duty_percentage for country: {country.get('origin_country')}")
#                 country['duty_percentage'] = 0.0
        
#         # Calculate scores
#         scores = self.calculate_country_scores(countries_data)
        
#         # Create a list of (country, score) tuples
#         country_scores = list(zip(countries_data, scores))
        
#         # Sort by score in descending order
#         sorted_countries = sorted(country_scores, key=lambda x: x[1], reverse=True)
        
#         # Take top 3 (or all if fewer than 3)
#         top_count = min(3, len(sorted_countries))
#         ranked_countries = []
        
#         for i in range(top_count):
#             country, score = sorted_countries[i]
#             # Create a copy to avoid modifying the original
#             ranked_country = country.copy()
#             # Add rank and score
#             ranked_country['Rank'] = i + 1
#             ranked_country['Score'] = round(score, 4)
#             ranked_countries.append(ranked_country)
        
#         return ranked_countries
    

import json
import logging
import requests
import statistics

from typing import List, Dict, Any
from app.config import TARIFF_SERVICE_BASE_URL, USE_LOCAL_HTS2DUTY
from app.services.hts2duty_service import get_hts2duty_service

logger = logging.getLogger(__name__)


# from app.utils import rank_suppliers, rank_countries, fetch_tariff_data, extract_duty_percentage

class TradeRankerService:

    def __init__(self):
        """Initialize the trade ranker service with enhanced ranking capabilities."""
        pass

    def rank_countries(self, countries, hs_code):
        """Rank countries based on FOB, shipment count, and tariff data"""
        enriched_countries = []
        unique_countries = {}

        for country_data in countries:
            country_name = country_data.get('origin_country')
            if not country_name:
                continue

            if country_name in unique_countries:
                existing = unique_countries[country_name]
                existing['shipment_count'] += country_data.get('shipment_count', 0)
                if country_data.get('average_fob', 0) > existing.get('average_fob', 0):
                    existing['average_fob'] = country_data.get('average_fob', 0)
            else:
                unique_countries[country_name] = {
                    'origin_country': country_name,
                    'average_fob': country_data.get('average_fob', 0),
                    'shipment_count': country_data.get('shipment_count', 0)
                }

        for country_name, country_data in unique_countries.items():
            logger.info("Ignoring fetching of tarif as the service might be down or take too long to respond")
            # try:
            #     tariff_data = TradeRankerService().fetch_tariff_data(country_name, hs_code)
            #     duty_str = tariff_data.get('Total Duty', '0%')
            #     duty_percentage = TradeRankerService().extract_duty_percentage(duty_str)

            #     country_data['Total Duty'] = duty_str
            #     country_data['duty_percentage'] = duty_percentage
            #     enriched_countries.append(country_data)
            # except Exception as e:
            #     logger.error(f"Failed to fetch tariff data for {country_name}: {str(e)}")
            country_data['Total Duty'] = 'N/A'
            country_data['duty_percentage'] = 0.0
            enriched_countries.append(country_data)

        return self.rank_enriched_countries(enriched_countries)



    def _calculate_supplier_scores(self, suppliers_data: List[Dict[str, Any]]) -> List[float]:
        """
        Calculate composite scores for suppliers based on multiple metrics.

        Args:
            suppliers_data: List of supplier data dictionaries

        Returns:
            List of calculated scores for each supplier
        """
        if not suppliers_data:
            return []

        # Extract metrics for scoring
        shipment_counts = []
        total_quantities = []
        unit_costs = []
        price_consistency = []

        for supplier in suppliers_data:
            # Shipment count (higher is better)
            shipment_counts.append(supplier.get('shipment_count', 0))

            # Total quantity (higher is better)
            total_quantities.append(supplier.get('total_quantity', 0))

            # Unit cost (lower is better)
            unit_costs.append(supplier.get('unit_cost', 0))

            # Price consistency (calculate as inverse of coefficient of variation)
            prices = supplier.get('prices', [])
            if len(prices) > 1:
                try:
                    mean_price = statistics.mean(prices)
                    std_price = statistics.stdev(prices)
                    cv = std_price / mean_price if mean_price > 0 else float('inf')
                    consistency = 1 / (1 + cv)  # Higher consistency = lower CV
                except (statistics.StatisticsError, ZeroDivisionError):
                    consistency = 0.5  # Default moderate consistency
            else:
                consistency = 1.0  # Perfect consistency for single price

            price_consistency.append(consistency)

        # Normalize all values to 0-1 scale
        norm_shipment_counts = self._normalize_values(shipment_counts, higher_is_better=True)
        norm_total_quantities = self._normalize_values(total_quantities, higher_is_better=True)
        norm_unit_costs = self._normalize_values(unit_costs, higher_is_better=False)
        norm_price_consistency = self._normalize_values(price_consistency, higher_is_better=True)

        # Calculate composite scores with equal weights (0.25 each)
        scores = []
        for i in range(len(suppliers_data)):
            score = (
                0.25 * norm_shipment_counts[i] +
                0.25 * norm_total_quantities[i] +
                0.25 * norm_unit_costs[i] +
                0.25 * norm_price_consistency[i]
            )
            scores.append(score)

        return scores

    def _normalize_values(self, values: List[float], higher_is_better: bool = True) -> List[float]:
        """
        Normalize a list of values to 0-1 scale.

        Args:
            values: List of values to normalize
            higher_is_better: If True, higher values get higher scores

        Returns:
            List of normalized values
        """
        if not values or all(v == 0 for v in values):
            return [0.0] * len(values)

        min_val = min(values)
        max_val = max(values)

        if min_val == max_val:
            return [1.0] * len(values)

        normalized = []
        for value in values:
            if higher_is_better:
                norm_value = (value - min_val) / (max_val - min_val)
            else:
                norm_value = (max_val - value) / (max_val - min_val)
            normalized.append(norm_value)

        return normalized

    def rank_suppliers(self, suppliers_data: List[Dict[str, Any]], top_n: int = 3) -> List[Dict[str, Any]]:
        """
        Rank suppliers based on composite scoring.

        Args:
            suppliers_data: List of supplier data dictionaries
            top_n: Number of top suppliers to return

        Returns:
            List of ranked suppliers with scores
        """
        if not suppliers_data:
            return []

        try:
            # Calculate scores for all suppliers
            scores = self._calculate_supplier_scores(suppliers_data)

            # Create a list of (supplier, score) tuples
            supplier_scores = list(zip(suppliers_data, scores))

            # Sort by score in descending order
            sorted_suppliers = sorted(supplier_scores, key=lambda x: x[1], reverse=True)

            # Take top N and add rank and score
            ranked_suppliers = []
            for i, (supplier, score) in enumerate(sorted_suppliers[:top_n]):
                # Create a copy to avoid modifying the original
                ranked_supplier = supplier.copy()
                # Add rank and score
                ranked_supplier['Rank'] = i + 1
                ranked_supplier['Score'] = round(score, 4)
                ranked_suppliers.append(ranked_supplier)

            return ranked_suppliers

        except Exception as e:
            logger.error(f"Error ranking suppliers: {str(e)}")
            return []

    def fetch_tariff_data(self, country, hs_code):
        """
        Fetch tariff data for a given country and HS code.
        Uses local HTS2Duty service if enabled, otherwise falls back to external API.

        Args:
            country (str): Country name
            hs_code (str): HS code

        Returns:
            dict: Tariff data including Total Duty
        """
        logger.debug(f"Fetching tariff data for country: {country}, HS code: {hs_code}")

        try:
            # Use local HTS2Duty service if enabled
            if USE_LOCAL_HTS2DUTY:
                logger.debug("Using local HTS2Duty service for tariff data")
                hts2duty_service = get_hts2duty_service()
                tariff_data = hts2duty_service.fetch_tariff_data(country, hs_code)

                # Check if there was an error in the response
                if 'error' in tariff_data:
                    logger.warning(f"HTS2Duty service error for {country}: {tariff_data['error']}")
                    # Fall back to external API if local service fails
                    return self._fetch_external_tariff_data(country, hs_code)

                logger.debug(f"Received tariff data from HTS2Duty for {country}: Total Duty = {tariff_data.get('Total Duty', 'N/A')}")
                return tariff_data
            else:
                # Use external API
                return self._fetch_external_tariff_data(country, hs_code)

        except Exception as e:
            logger.error(f"Error in fetch_tariff_data for {country}: {str(e)}")
            # Try external API as fallback if local service fails
            if USE_LOCAL_HTS2DUTY:
                logger.info(f"Falling back to external API for {country}")
                try:
                    return self._fetch_external_tariff_data(country, hs_code)
                except Exception as fallback_error:
                    logger.error(f"External API fallback also failed for {country}: {str(fallback_error)}")
            # Re-raise the exception for the caller to handle
            raise

    def _fetch_external_tariff_data(self, country, hs_code):
        """
        Fetch tariff data from external API service.

        Args:
            country (str): Country name
            hs_code (str): HS code

        Returns:
            dict: Tariff data including Total Duty
        """
        logger.debug(f"Fetching tariff data from external API for country: {country}, HS code: {hs_code}")

        # Make the request to the external service
        response = requests.get(f"{TARIFF_SERVICE_BASE_URL}/tariff?country={country}&hs_code={hs_code}")

        # Check if the request was successful
        if response.status_code == 200:
            # The response might be a JSON string inside a JSON response
            # First, try to parse as regular JSON
            try:
                tariff_data = response.json()

                # If the result is a string, it might be a JSON string that needs further parsing
                if isinstance(tariff_data, str):
                    tariff_data = json.loads(tariff_data)

                logger.debug(f"Received tariff data from external API for {country}: Total Duty = {tariff_data.get('Total Duty', 'N/A')}")
                return tariff_data

            except json.JSONDecodeError as e:
                # If JSON parsing fails, try to extract as plain text and parse manually
                logger.error(f"JSON parsing error for {country}: {str(e)}")

                # Try to parse the raw response text as JSON
                try:
                    tariff_data = json.loads(response.text)
                    logger.debug(f"Successfully parsed response text as JSON for {country}")
                    return tariff_data
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse response for {country} in any format")
                    raise Exception(f"Invalid response format from tariff API for {country}")
        else:
            logger.error(f"API request failed for {country} with status code {response.status_code}")
            raise Exception(f"API request failed with status {response.status_code}")

    # Function to extract numerical duty percentage from string (e.g., "6.5%" -> 6.5)
    def extract_duty_percentage(self, duty_str):
        """Extract numerical percentage from duty string."""
        if not duty_str or duty_str == "NA":
            return 0.0

        # Handle the case where duty_str is already a number
        if isinstance(duty_str, (int, float)):
            return float(duty_str)

        # Remove % symbol and convert to float
        try:
            return float(duty_str.replace("%", ""))
        except (ValueError, AttributeError) as e:
            logger.error(f"Could not parse duty string: {duty_str} - Error: {str(e)}")
            return 0.0

    # Function to calculate country scores based on average FOB, shipment count, and duty
    def calculate_country_scores(self, countries_data):
        """
        Calculate composite scores for countries based on:
        - average_fob (higher is better)
        - shipment_count (higher is better)
        - duty_percentage (lower is better)

        With highest weight on duty_percentage.
        """
        # Extract values for normalization
        avg_fobs = [country.get('average_fob', 0) for country in countries_data]
        shipment_counts = [country.get('shipment_count', 0) for country in countries_data]
        duty_percentages = [country.get('duty_percentage', 0) for country in countries_data]

        # Normalize values
        norm_avg_fobs = self._normalize_values(avg_fobs, higher_is_better=True)
        norm_shipment_counts = self._normalize_values(shipment_counts, higher_is_better=True)
        norm_duty_percentages = self._normalize_values(duty_percentages, higher_is_better=False)

        # Calculate composite scores with weights (higher weight for duty)
        # Weight distribution: 20% FOB, 20% shipment count, 60% duty
        scores = []
        for i in range(len(countries_data)):
            score = (
                0.20 * norm_avg_fobs[i] +
                0.20 * norm_shipment_counts[i] +
                0.60 * norm_duty_percentages[i]
            )
            scores.append(score)

        return scores

    # Function to rank countries based on composite scores
    def rank_enriched_countries(self, countries_data):
        """Rank countries based on composite scores and return top 3 with rank and score."""
        # First, ensure each country has all required fields
        for country in countries_data:
            if 'duty_percentage' not in country:
                logger.warning(f"Missing duty_percentage for country: {country.get('origin_country')}")
                country['duty_percentage'] = 0.0

        # Use the integrated ranking functionality
        return self.rank_countries_with_scores(countries_data, top_n=3)

    def rank_countries_with_scores(self, countries_data: List[Dict[str, Any]], top_n: int = 3) -> List[Dict[str, Any]]:
        """
        Rank countries based on composite scoring.

        Args:
            countries_data: List of country data dictionaries
            top_n: Number of top countries to return

        Returns:
            List of ranked countries with scores
        """
        if not countries_data:
            return []

        try:
            # Calculate scores for all countries
            scores = self.calculate_country_scores(countries_data)

            # Create a list of (country, score) tuples
            country_scores = list(zip(countries_data, scores))

            # Sort by score in descending order
            sorted_countries = sorted(country_scores, key=lambda x: x[1], reverse=True)

            # Take top N and add rank and score
            ranked_countries = []
            for i, (country, score) in enumerate(sorted_countries[:top_n]):
                # Create a copy to avoid modifying the original
                ranked_country = country.copy()
                # Add rank and score
                ranked_country['Rank'] = i + 1
                ranked_country['Score'] = round(score, 4)
                ranked_countries.append(ranked_country)

            return ranked_countries

        except Exception as e:
            logger.error(f"Error ranking countries: {str(e)}")
            return []
