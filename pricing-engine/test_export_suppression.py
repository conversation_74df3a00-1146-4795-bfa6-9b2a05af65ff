#!/usr/bin/env python3
"""
Test script to verify export data suppression functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.trade_finder_service import (
    is_us_destination, 
    should_suppress_export_data, 
    get_effective_destination,
    TradeFinderService
)

def test_destination_detection():
    """Test US destination detection"""
    print("Testing US destination detection...")
    
    # Test US destinations
    us_destinations = [
        "United States",
        "USA", 
        "US",
        "america",
        "United States of America",
        "  usa  ",  # with whitespace
        "UNITED STATES"  # uppercase
    ]
    
    for dest in us_destinations:
        result = is_us_destination(dest)
        print(f"  '{dest}' -> US: {result}")
        assert result == True, f"Expected True for '{dest}'"
    
    # Test non-US destinations
    non_us_destinations = [
        "Thailand",
        "India", 
        "China",
        "Germany",
        "Canada",
        "",
        None
    ]
    
    for dest in non_us_destinations:
        result = is_us_destination(dest)
        print(f"  '{dest}' -> US: {result}")
        assert result == False, f"Expected False for '{dest}'"
    
    print("✓ US destination detection tests passed\n")

def test_export_suppression():
    """Test export data suppression logic"""
    print("Testing export data suppression...")
    
    # Test with suppression enabled (default)
    test_cases = [
        ("United States", False),  # US should not be suppressed
        ("Thailand", True),        # Non-US should be suppressed
        ("India", True),           # Non-US should be suppressed
        ("USA", False),            # US variant should not be suppressed
    ]
    
    for dest, expected_suppressed in test_cases:
        result = should_suppress_export_data(dest)
        print(f"  '{dest}' -> Suppressed: {result}")
        assert result == expected_suppressed, f"Expected {expected_suppressed} for '{dest}'"
    
    print("✓ Export suppression tests passed\n")

def test_effective_destination():
    """Test effective destination logic"""
    print("Testing effective destination logic...")
    
    test_cases = [
        ("United States", "United States"),  # US should remain unchanged
        ("Thailand", "United States"),       # Non-US should become US
        ("India", "United States"),          # Non-US should become US
        ("USA", "USA"),                      # US variant should remain unchanged
    ]
    
    for original, expected in test_cases:
        result = get_effective_destination(original)
        print(f"  '{original}' -> '{result}'")
        assert result == expected, f"Expected '{expected}' for '{original}', got '{result}'"
    
    print("✓ Effective destination tests passed\n")

def test_service_integration():
    """Test integration with TradeFinderService"""
    print("Testing service integration...")
    
    try:
        service = TradeFinderService()
        print("✓ TradeFinderService instantiated successfully")
        
        # Test that the service has the new method
        assert hasattr(service, '_add_export_suppression_metadata'), "Service missing _add_export_suppression_metadata method"
        print("✓ Service has export suppression metadata method")
        
        # Test metadata addition
        test_result = {
            'data': [{'test': 'data'}],
            'metadata': {'existing': 'data'}
        }
        
        # Test with suppression
        updated_result = service._add_export_suppression_metadata(test_result, "Thailand", "United States")
        assert updated_result['metadata']['export_data_suppressed'] == True
        assert updated_result['metadata']['original_destination'] == "Thailand"
        assert updated_result['metadata']['effective_destination'] == "United States"
        print("✓ Metadata addition with suppression works")
        
        # Test without suppression
        test_result2 = {'data': [{'test': 'data'}]}
        updated_result2 = service._add_export_suppression_metadata(test_result2, "United States", "United States")
        assert updated_result2['metadata']['export_data_suppressed'] == False
        print("✓ Metadata addition without suppression works")
        
    except Exception as e:
        print(f"✗ Service integration test failed: {e}")
        return False
    
    print("✓ Service integration tests passed\n")
    return True

if __name__ == "__main__":
    print("Running export data suppression tests...\n")
    
    try:
        test_destination_detection()
        test_export_suppression()
        test_effective_destination()
        test_service_integration()
        
        print("🎉 All tests passed! Export data suppression is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
