#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to verify that the top-suppliers-by-geography-tariff endpoint is using HTS2Duty
"""

import sys
import os
import requests
import json
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_configuration():
    """Test if HTS2Duty configuration is properly loaded"""
    print("🔧 Testing HTS2Duty Configuration...")
    
    try:
        from app.config import USE_LOCAL_HTS2DUTY, HTS2DUTY_SERVICE_URL
        print(f"✓ USE_LOCAL_HTS2DUTY: {USE_LOCAL_HTS2DUTY}")
        print(f"✓ HTS2DUTY_SERVICE_URL: {HTS2DUTY_SERVICE_URL}")
        
        if USE_LOCAL_HTS2DUTY:
            print("✅ HTS2Duty is ENABLED in configuration")
        else:
            print("❌ HTS2Duty is DISABLED in configuration")
            
        return USE_LOCAL_HTS2DUTY
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False

def test_hts2duty_service_directly():
    """Test the HTS2Duty service directly"""
    print("\n🧪 Testing HTS2Duty Service Directly...")
    
    try:
        from app.services.hts2duty_service import get_hts2duty_service
        
        print("✓ Importing HTS2Duty service...")
        hts2duty_service = get_hts2duty_service()
        print("✓ HTS2Duty service instantiated")
        
        # Test with a sample HS code and country
        test_hs_code = "39073010"
        test_country = "Germany"
        
        print(f"✓ Testing tariff lookup for HS: {test_hs_code}, Country: {test_country}")
        start_time = time.time()
        tariff_data = hts2duty_service.get_tariff_data(test_hs_code, test_country)
        end_time = time.time()
        
        print(f"✓ HTS2Duty response time: {(end_time - start_time):.2f} seconds")
        
        if 'error' in tariff_data:
            print(f"❌ HTS2Duty returned error: {tariff_data['error']}")
            return False
        else:
            print("✅ HTS2Duty service working correctly")
            print(f"   Sample response: {json.dumps(tariff_data, indent=2)[:200]}...")
            return True
            
    except Exception as e:
        print(f"❌ Error testing HTS2Duty service: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_finder_service():
    """Test the TradeFinderService to see if it uses HTS2Duty"""
    print("\n🔍 Testing TradeFinderService HTS2Duty Integration...")
    
    try:
        from app.services.trade_finder_service import TradeFinderService
        
        service = TradeFinderService()
        print(f"✓ TradeFinderService instantiated")
        print(f"✓ use_local_hts2duty setting: {service.use_local_hts2duty}")
        
        if service.use_local_hts2duty:
            print("✅ TradeFinderService is configured to use HTS2Duty")
        else:
            print("❌ TradeFinderService is NOT configured to use HTS2Duty")
            
        # Test the _fetch_tariff_data method directly
        print("✓ Testing _fetch_tariff_data method...")
        tariff_data = service._fetch_tariff_data("39073010", "Germany")
        
        if 'error' in tariff_data:
            print(f"❌ _fetch_tariff_data returned error: {tariff_data['error']}")
            return False
        else:
            print("✅ _fetch_tariff_data working correctly")
            return True
            
    except Exception as e:
        print(f"❌ Error testing TradeFinderService: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint():
    """Test the actual API endpoint"""
    print("\n🌐 Testing API Endpoint...")
    
    # Check if server is running
    try:
        health_response = requests.get("http://localhost:5001/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ Server is not running or health check failed")
            return False
        print("✓ Server is running")
    except:
        print("❌ Server is not running. Please start the server first.")
        return False
    
    # Test the tariff endpoint
    try:
        endpoint = "http://localhost:5001/v1/trade/top-suppliers-by-geography-tariff"
        params = {
            "hs_code": "39073010",
            "destination": "United States",  # Use US to avoid suppression
            "months": 12
        }
        
        print(f"✓ Calling endpoint: {endpoint}")
        print(f"✓ Parameters: {params}")
        
        start_time = time.time()
        response = requests.get(endpoint, params=params, timeout=30)
        end_time = time.time()
        
        print(f"✓ Response time: {(end_time - start_time):.2f} seconds")
        print(f"✓ Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Check if we have tariff data
            if 'result' in data and 'data' in data['result']:
                result_data = data['result']['data']
                if result_data and len(result_data) > 0:
                    first_entry = result_data[0]
                    if 'tariff_info' in first_entry:
                        tariff_info = first_entry['tariff_info']
                        print("✅ API endpoint returned tariff data")
                        print(f"   Sample tariff info: {json.dumps(tariff_info, indent=2)[:200]}...")
                        
                        # Check for HTS2Duty specific fields
                        if isinstance(tariff_info, dict) and 'hts_no' in tariff_info:
                            print("✅ Tariff data contains HTS2Duty format (hts_no field)")
                            return True
                        else:
                            print("⚠️  Tariff data format unclear")
                            return True
                    else:
                        print("❌ No tariff_info in response data")
                        return False
                else:
                    print("❌ No data in response")
                    return False
            else:
                print("❌ Invalid response structure")
                return False
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API endpoint: {e}")
        return False

def main():
    print("🔍 HTS2Duty Usage Verification Script")
    print("=" * 50)
    
    results = []
    
    # Test 1: Configuration
    config_ok = test_configuration()
    results.append(("Configuration", config_ok))
    
    # Test 2: HTS2Duty Service
    service_ok = test_hts2duty_service_directly()
    results.append(("HTS2Duty Service", service_ok))
    
    # Test 3: TradeFinderService Integration
    integration_ok = test_trade_finder_service()
    results.append(("TradeFinderService Integration", integration_ok))
    
    # Test 4: API Endpoint
    api_ok = test_api_endpoint()
    results.append(("API Endpoint", api_ok))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:30} {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED - HTS2Duty is being used correctly!")
    else:
        print("⚠️  SOME TESTS FAILED - Check the issues above")
    
    print("\n💡 Key Indicators that HTS2Duty is being used:")
    print("   1. Configuration shows USE_LOCAL_HTS2DUTY=True")
    print("   2. Log messages show 'Using local HTS2Duty service for tariff data'")
    print("   3. Tariff responses contain 'hts_no' field (HTS2Duty format)")
    print("   4. Response times are fast (local service vs external API)")

if __name__ == "__main__":
    main()
