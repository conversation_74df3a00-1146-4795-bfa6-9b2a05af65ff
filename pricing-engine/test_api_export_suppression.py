#!/usr/bin/env python3
"""
Test script to verify export data suppression functionality via API
"""

import requests
import json
import sys

def test_api_endpoint(base_url="http://localhost:5001"):
    """Test the API endpoint with different destinations"""
    
    print("Testing API export data suppression...\n")
    
    # Test data
    test_cases = [
        {
            "name": "US Destination (should not be suppressed)",
            "params": {
                "hs_code": "3907.30",
                "destination": "United States",
                "months": 12
            },
            "expected_suppressed": False
        },
        {
            "name": "Thailand Destination (should be suppressed)",
            "params": {
                "hs_code": "3907.30", 
                "destination": "Thailand",
                "months": 12
            },
            "expected_suppressed": True
        },
        {
            "name": "India Destination (should be suppressed)",
            "params": {
                "hs_code": "3907.30",
                "destination": "India", 
                "months": 12
            },
            "expected_suppressed": True
        }
    ]
    
    endpoint = f"{base_url}/trade/top-suppliers-by-geography"
    
    for test_case in test_cases:
        print(f"Testing: {test_case['name']}")
        print(f"Parameters: {test_case['params']}")
        
        try:
            response = requests.get(endpoint, params=test_case['params'], timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check if the response has the expected structure
                if 'result' in data and 'metadata' in data['result']:
                    metadata = data['result']['metadata']
                    suppressed = metadata.get('export_data_suppressed', False)
                    
                    print(f"  Response received successfully")
                    print(f"  Export data suppressed: {suppressed}")
                    
                    if 'original_destination' in metadata:
                        print(f"  Original destination: {metadata['original_destination']}")
                    if 'effective_destination' in metadata:
                        print(f"  Effective destination: {metadata['effective_destination']}")
                    if 'suppression_reason' in metadata:
                        print(f"  Suppression reason: {metadata['suppression_reason']}")
                    
                    # Verify expectation
                    if suppressed == test_case['expected_suppressed']:
                        print(f"  ✓ Test passed - suppression behavior as expected")
                    else:
                        print(f"  ✗ Test failed - expected suppressed={test_case['expected_suppressed']}, got {suppressed}")
                        
                else:
                    print(f"  ⚠ Response missing expected metadata structure")
                    print(f"  Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                    
            else:
                print(f"  ✗ API request failed with status {response.status_code}")
                print(f"  Response: {response.text[:200]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"  ✗ Request failed: {e}")
        except Exception as e:
            print(f"  ✗ Unexpected error: {e}")
            
        print()

def test_trade_data_by_country_endpoint(base_url="http://localhost:5001"):
    """Test the trade-data-by-country endpoint"""
    
    print("Testing trade-data-by-country endpoint...\n")
    
    test_params = {
        "hs_code": "3907.30",
        "origin": "China",
        "destination": "Thailand",  # Non-US destination
        "months": 12
    }
    
    endpoint = f"{base_url}/trade/trade-data-by-country"
    
    print(f"Testing trade-data-by-country with Thailand destination")
    print(f"Parameters: {test_params}")
    
    try:
        response = requests.get(endpoint, params=test_params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'result' in data and 'metadata' in data['result']:
                metadata = data['result']['metadata']
                suppressed = metadata.get('export_data_suppressed', False)
                
                print(f"  Response received successfully")
                print(f"  Export data suppressed: {suppressed}")
                
                if suppressed:
                    print(f"  Original destination: {metadata.get('original_destination')}")
                    print(f"  Effective destination: {metadata.get('effective_destination')}")
                    print(f"  ✓ Non-US destination correctly suppressed")
                else:
                    print(f"  ⚠ Expected suppression for non-US destination")
                    
            else:
                print(f"  ⚠ Response missing expected metadata structure")
                
        else:
            print(f"  ✗ API request failed with status {response.status_code}")
            
    except Exception as e:
        print(f"  ✗ Error: {e}")
        
    print()

if __name__ == "__main__":
    print("Testing export data suppression via API...\n")
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:5001/health", timeout=5)
        if response.status_code == 200:
            print("✓ Server is running\n")
        else:
            print("⚠ Server responded but health check failed\n")
    except:
        print("❌ Server is not running. Please start the server first.")
        print("Run: python main.py")
        sys.exit(1)
    
    # Run tests
    test_api_endpoint()
    test_trade_data_by_country_endpoint()
    
    print("API testing completed!")
