#!/usr/bin/env python3
"""
Debug script to test export data suppression configuration and logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_loading():
    """Test if configuration is loaded correctly"""
    print("Testing configuration loading...")
    
    try:
        from app.config import SUPPRESS_EXPORT_DATA_FOR_NON_US, DEFAULT_EXPORT_DESTINATION
        print(f"✓ SUPPRESS_EXPORT_DATA_FOR_NON_US: {SUPPRESS_EXPORT_DATA_FOR_NON_US}")
        print(f"✓ DEFAULT_EXPORT_DESTINATION: {DEFAULT_EXPORT_DESTINATION}")
        return True
    except ImportError as e:
        print(f"✗ Failed to import config: {e}")
        return False
    except Exception as e:
        print(f"✗ Error loading config: {e}")
        return False

def test_suppression_functions():
    """Test the suppression functions directly"""
    print("\nTesting suppression functions...")
    
    try:
        from app.services.trade_finder_service import (
            is_us_destination, 
            should_suppress_export_data, 
            get_effective_destination
        )
        
        # Test Thailand (should be suppressed)
        thailand_is_us = is_us_destination("Thailand")
        thailand_should_suppress = should_suppress_export_data("Thailand")
        thailand_effective = get_effective_destination("Thailand")
        
        print(f"Thailand:")
        print(f"  is_us_destination: {thailand_is_us}")
        print(f"  should_suppress_export_data: {thailand_should_suppress}")
        print(f"  get_effective_destination: '{thailand_effective}'")
        
        # Test US (should not be suppressed)
        us_is_us = is_us_destination("United States")
        us_should_suppress = should_suppress_export_data("United States")
        us_effective = get_effective_destination("United States")
        
        print(f"United States:")
        print(f"  is_us_destination: {us_is_us}")
        print(f"  should_suppress_export_data: {us_should_suppress}")
        print(f"  get_effective_destination: '{us_effective}'")
        
        # Verify expected behavior
        if thailand_should_suppress and thailand_effective == "United States":
            print("✓ Thailand suppression working correctly")
        else:
            print("✗ Thailand suppression not working")
            
        if not us_should_suppress and us_effective == "United States":
            print("✓ US non-suppression working correctly")
        else:
            print("✗ US non-suppression not working")
            
        return True
        
    except Exception as e:
        print(f"✗ Error testing suppression functions: {e}")
        return False

def test_service_method():
    """Test the service method directly"""
    print("\nTesting TradeFinderService method...")
    
    try:
        from app.services.trade_finder_service import TradeFinderService
        
        service = TradeFinderService()
        
        # Test metadata function
        test_result = {
            'data': [{'test': 'data'}],
            'metadata': {'existing': 'data'}
        }
        
        # Test with Thailand (should be suppressed)
        updated_result = service._add_export_suppression_metadata(
            test_result.copy(), 
            "Thailand", 
            "United States"
        )
        
        print("Metadata test with Thailand -> United States:")
        print(f"  export_data_suppressed: {updated_result['metadata'].get('export_data_suppressed')}")
        print(f"  original_destination: {updated_result['metadata'].get('original_destination')}")
        print(f"  effective_destination: {updated_result['metadata'].get('effective_destination')}")
        
        if updated_result['metadata'].get('export_data_suppressed') == True:
            print("✓ Metadata suppression working correctly")
        else:
            print("✗ Metadata suppression not working")
            
        return True
        
    except Exception as e:
        print(f"✗ Error testing service method: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_variables():
    """Test environment variables directly"""
    print("\nTesting environment variables...")
    
    import os
    from dotenv import load_dotenv
    
    # Load .env file
    load_dotenv()
    
    suppress_env = os.getenv('SUPPRESS_EXPORT_DATA_FOR_NON_US')
    default_dest_env = os.getenv('DEFAULT_EXPORT_DESTINATION')
    
    print(f"Raw environment variables:")
    print(f"  SUPPRESS_EXPORT_DATA_FOR_NON_US: '{suppress_env}'")
    print(f"  DEFAULT_EXPORT_DESTINATION: '{default_dest_env}'")
    
    # Test conversion
    suppress_bool = suppress_env.lower() == 'true' if suppress_env else False
    print(f"  Converted to boolean: {suppress_bool}")
    
    return True

if __name__ == "__main__":
    print("🔍 Debugging export data suppression...\n")
    
    success = True
    
    success &= test_environment_variables()
    success &= test_config_loading()
    success &= test_suppression_functions()
    success &= test_service_method()
    
    if success:
        print("\n🎉 All debug tests passed!")
    else:
        print("\n❌ Some debug tests failed!")
        sys.exit(1)
