# Export Data Suppression Implementation

## Overview

This implementation adds functionality to suppress export data for non-US destinations in the pricing engine. When a user requests export data for a destination country other than the United States, the system will automatically return US export data instead, while clearly indicating that the data has been suppressed.

## Key Features

1. **Automatic Detection**: Identifies when destination country is not the United States
2. **Data Substitution**: Returns US export data for non-US destinations
3. **Transparent Metadata**: Clearly indicates when data suppression has occurred
4. **Configurable**: Can be enabled/disabled via environment variables

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Export Data Handling Configuration
SUPPRESS_EXPORT_DATA_FOR_NON_US=true
DEFAULT_EXPORT_DESTINATION=United States
```

- `SUPPRESS_EXPORT_DATA_FOR_NON_US`: Enable/disable export data suppression (default: true)
- `DEFAULT_EXPORT_DESTINATION`: Default destination to use when suppressing (default: "United States")

## Implementation Details

### Core Functions

#### `is_us_destination(destination: str) -> bool`
Determines if a destination country is the United States. Recognizes various formats:
- "United States"
- "USA" 
- "US"
- "America"
- "United States of America"
- Case-insensitive and whitespace-tolerant

#### `should_suppress_export_data(destination: str) -> bool`
Determines if export data should be suppressed based on:
- Configuration setting (`SUPPRESS_EXPORT_DATA_FOR_NON_US`)
- Whether destination is US or non-US

#### `get_effective_destination(destination: str) -> str`
Returns the effective destination to use for queries:
- Returns original destination if US or suppression disabled
- Returns `DEFAULT_EXPORT_DESTINATION` if non-US and suppression enabled

### Service Layer Changes

Modified `TradeFinderService` methods:
- `get_top_suppliers_by_geography()`
- `get_top_suppliers_by_geography_tariff()`
- `get_trade_data_by_country()`
- `get_top_suppliers()`

Each method now:
1. Applies destination filtering logic
2. Uses effective destination for database queries
3. Adds suppression metadata to responses

### Metadata Enhancement

When export data is suppressed, the response includes additional metadata:

```json
{
  "metadata": {
    "export_data_suppressed": true,
    "original_destination": "Thailand",
    "effective_destination": "United States", 
    "suppression_reason": "Export data suppressed for non-US destination 'Thailand'. Showing US data instead."
  }
}
```

When data is not suppressed:

```json
{
  "metadata": {
    "export_data_suppressed": false
  }
}
```

## API Endpoints Affected

All trade data endpoints are affected:

1. **GET /trade/top-suppliers-by-geography**
   - Returns top supplier countries by geography
   - Suppresses data for non-US destinations

2. **GET /trade/top-suppliers-by-geography-tariff**
   - Returns top supplier countries with tariff information
   - Suppresses data for non-US destinations

3. **GET /trade/trade-data-by-country**
   - Returns trade data for specific country pairs
   - Suppresses data when destination is non-US

4. **GET /trade/top-suppliers**
   - Returns top suppliers for specific routes
   - Suppresses data when destination is non-US

5. **POST /trade/top-exporters**
   - Enhanced country lookup endpoints
   - Suppresses data for non-US destinations

## Example Usage

### Request for US Destination (No Suppression)
```bash
curl "http://localhost:5001/trade/top-suppliers-by-geography?hs_code=3907.30&destination=United%20States&months=12"
```

Response metadata:
```json
{
  "metadata": {
    "export_data_suppressed": false,
    "row_count": 15,
    "query_time_ms": 234.5
  }
}
```

### Request for Non-US Destination (Suppressed)
```bash
curl "http://localhost:5001/trade/top-suppliers-by-geography?hs_code=3907.30&destination=Thailand&months=12"
```

Response metadata:
```json
{
  "metadata": {
    "export_data_suppressed": true,
    "original_destination": "Thailand",
    "effective_destination": "United States",
    "suppression_reason": "Export data suppressed for non-US destination 'Thailand'. Showing US data instead.",
    "row_count": 15,
    "query_time_ms": 234.5
  }
}
```

## Testing

### Unit Tests
Run the unit tests to verify functionality:
```bash
python test_export_suppression.py
```

### API Tests
Test the API endpoints (requires running server):
```bash
python test_api_export_suppression.py
```

## Logging

The system logs suppression events:
```
INFO - Suppressing export data for non-US destination 'Thailand', using 'United States' instead
```

## Backward Compatibility

- Existing API contracts remain unchanged
- Additional metadata is added without breaking existing clients
- Configuration allows disabling the feature if needed

## Security Considerations

- Input validation remains in place for all destination parameters
- Suppression logic is applied after validation
- No sensitive information is exposed in suppression metadata
