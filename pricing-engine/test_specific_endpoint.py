#!/usr/bin/env python3
"""
Test the specific endpoint that's showing Thailand data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_tariff_endpoint_directly():
    """Test the tariff endpoint method directly"""
    print("Testing get_top_suppliers_by_geography_tariff method directly...")
    
    try:
        from app.services.trade_finder_service import TradeFinderService
        
        service = TradeFinderService()
        
        # Test with Thailand destination and HS code 39073010
        print("Calling get_top_suppliers_by_geography_tariff with:")
        print("  hs_code: '39073010'")
        print("  months: 12")
        print("  destination: 'Thailand'")
        print("  chemical_name: None")
        
        result = service.get_top_suppliers_by_geography_tariff(
            hs_code="39073010",
            months=12,
            destination="Thailand",
            chemical_name=None
        )
        
        print(f"\nResult metadata:")
        metadata = result.get('metadata', {})
        for key, value in metadata.items():
            print(f"  {key}: {value}")
        
        print(f"\nData count: {len(result.get('data', []))}")
        
        # Check if suppression metadata is present
        if metadata.get('export_data_suppressed'):
            print("✓ Export data suppression is working!")
            print(f"  Original destination: {metadata.get('original_destination')}")
            print(f"  Effective destination: {metadata.get('effective_destination')}")
        else:
            print("✗ Export data suppression is NOT working!")
            print("  This means the data you're seeing is actual Thailand data, not US data")
        
        # Show first few data points
        data = result.get('data', [])
        if data:
            print(f"\nFirst data entry:")
            first_entry = data[0]
            for key, value in first_entry.items():
                if key != 'tariff_info':  # Skip tariff_info for brevity
                    print(f"  {key}: {value}")
        
        return result
        
    except Exception as e:
        print(f"✗ Error testing tariff endpoint: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_base_geography_method():
    """Test the base geography method"""
    print("\n" + "="*50)
    print("Testing get_top_suppliers_by_geography method directly...")
    
    try:
        from app.services.trade_finder_service import TradeFinderService
        
        service = TradeFinderService()
        
        print("Calling get_top_suppliers_by_geography with:")
        print("  hs_code: '39073010'")
        print("  months: 12")
        print("  destination: 'Thailand'")
        print("  chemical_name: None")
        
        result = service.get_top_suppliers_by_geography(
            hs_code="39073010",
            months=12,
            destination="Thailand",
            chemical_name=None
        )
        
        print(f"\nResult metadata:")
        metadata = result.get('metadata', {})
        for key, value in metadata.items():
            print(f"  {key}: {value}")
        
        print(f"\nData count: {len(result.get('data', []))}")
        
        # Check if suppression metadata is present
        if metadata.get('export_data_suppressed'):
            print("✓ Export data suppression is working in base method!")
        else:
            print("✗ Export data suppression is NOT working in base method!")
        
        return result
        
    except Exception as e:
        print(f"✗ Error testing base geography method: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_effective_destination_in_query():
    """Test what destination is actually being used in the query"""
    print("\n" + "="*50)
    print("Testing effective destination logic...")
    
    try:
        from app.services.trade_finder_service import get_effective_destination
        
        original_dest = "Thailand"
        effective_dest = get_effective_destination(original_dest)
        
        print(f"Original destination: '{original_dest}'")
        print(f"Effective destination: '{effective_dest}'")
        
        if effective_dest == "United States":
            print("✓ Destination conversion is working correctly")
        else:
            print("✗ Destination conversion is NOT working")
            
        return effective_dest
        
    except Exception as e:
        print(f"✗ Error testing effective destination: {e}")
        return None

if __name__ == "__main__":
    print("🔍 Testing specific endpoint behavior...\n")
    
    # Test the effective destination logic first
    effective_dest = test_effective_destination_in_query()
    
    # Test the base geography method
    base_result = test_base_geography_method()
    
    # Test the tariff endpoint
    tariff_result = test_tariff_endpoint_directly()
    
    print("\n" + "="*50)
    print("SUMMARY:")
    
    if effective_dest == "United States":
        print("✓ Destination conversion: WORKING")
    else:
        print("✗ Destination conversion: NOT WORKING")
    
    if base_result and base_result.get('metadata', {}).get('export_data_suppressed'):
        print("✓ Base geography method: WORKING")
    else:
        print("✗ Base geography method: NOT WORKING")
    
    if tariff_result and tariff_result.get('metadata', {}).get('export_data_suppressed'):
        print("✓ Tariff method: WORKING")
    else:
        print("✗ Tariff method: NOT WORKING")
    
    print("\nIf you're still seeing Thailand data, it means:")
    print("1. The server might need to be restarted to pick up the changes")
    print("2. There might be caching involved")
    print("3. You might be hitting a different endpoint")
    print("4. The database might actually have US data labeled as Thailand data")
